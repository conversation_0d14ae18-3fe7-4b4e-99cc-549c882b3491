const crypto = require('crypto');

function generateReferenceCode() {
    const prefix = 'BK';
    const datePart = new Date().toISOString().slice(0, 10).replace(/-/g, ''); // YYYYMMDD
    const randomPart = crypto
        .randomBytes(3)
        .toString('hex')
        .toUpperCase()
        .slice(0, 6); // e.g., '4F7X2Q'
    return `${prefix}-${datePart}${randomPart}`;
}

exports.generateReferenceCode = generateReferenceCode;
