const { body } = require('express-validator');

const validateHoliday = [
    body('startDate')
        .notEmpty()
        .withMessage('startDate is required')
        .isISO8601()
        .withMessage('startDate must be a valid date'),

    body('endDate')
        .notEmpty()
        .withMessage('endDate is required')
        .isISO8601()
        .withMessage('endDate must be a valid date'),

    body('reason').optional().isString().withMessage('reason must be a string'),

    body('approved')
        .optional()
        .isBoolean()
        .withMessage('approved must be a boolean'),

    body('notes').optional().isString().withMessage('notes must be a string'),
];

module.exports = {
    validateHoliday,
};
