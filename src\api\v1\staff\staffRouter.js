const express = require('express');

const router = express.Router();

const StaffController = require('./controller/staffController');

const StaffHolidayController = require('./controller/staffHolidayController');

const { validateStaff } = require('./middleware/staffMiddleware');

const { validateHoliday } = require('./middleware/staffHolidayMiddleware');

const fetchAuth = require('../../common/utils/communicator');

// ------- STAFF ROUTES -------
router.post(
    '/',
    validateStaff,
    fetchAuth.fetchAuthProviderDataMiddleware,
    StaffController.createStaff
);

router.get(
    '/',
    fetchAuth.fetchAuthProviderDataMiddleware,
    StaffController.getStaff
);

router.get(
    '/:staffId',
    fetchAuth.fetchAuthProviderDataMiddleware,
    StaffController.getStaffById
);

router.put(
    '/:staffId',
    validateStaff,
    fetchAuth.fetchAuthProviderDataMiddleware,
    StaffController.updateStaff
);

router.delete(
    '/:staffId',
    fetchAuth.fetchAuthProviderDataMiddleware,
    StaffController.deleteStaff
);

// ------- STAFF HOLIDAY ROUTES -------
router.post(
    '/:staffId/holidays',
    validateHoliday,
    fetchAuth.fetchAuthProviderDataMiddleware,
    StaffHolidayController.createStaffHoliday
);

router.get(
    '/holidays/all',
    fetchAuth.fetchAuthProviderDataMiddleware,
    StaffHolidayController.getAllHolidays
);

router.get(
    '/holidays/:holidayId',
    fetchAuth.fetchAuthProviderDataMiddleware,
    StaffHolidayController.getHolidayByHolidayId
);

router.get(
    '/:staffId/holidays',
    fetchAuth.fetchAuthProviderDataMiddleware,
    StaffHolidayController.getHolidaysByStaffId
);

router.put(
    '/holidays/:holidayId',
    validateHoliday,
    fetchAuth.fetchAuthProviderDataMiddleware,
    StaffHolidayController.updateStaffHoliday
);

router.delete(
    '/holidays/:holidayId',
    fetchAuth.fetchAuthProviderDataMiddleware,
    StaffHolidayController.deleteStaffHoliday
);

module.exports = router;
