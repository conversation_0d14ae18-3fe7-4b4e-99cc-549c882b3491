const { validationResult } = require('express-validator');

const { InsufficientScopeError } = require('express-oauth2-jwt-bearer');

const staffHolidayService = require('../service/staffHolidayService');

const logger = require('../../../common/utils/logger');

const errorUtil = require('../../../common/utils/error');

const {
    validateAndParseParams,
    buildQueryFilter,
} = require('../../../common/utils/filter');

const createStaffHoliday = async (req, res) => {
    const errorId = errorUtil.generateErrorId();
    try {
        logger.info('Received request to create staff holiday', { errorId });

        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const formattedErrors = errors.array().map((err) => ({
                field: err.path,
                message: err.msg,
            }));

            return res
                .status(422)
                .json(
                    errorUtil.createErrorResponse(
                        formattedErrors,
                        errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                        422,
                        errorId
                    )
                );
        }

        const staffId = req.params.staffId;
        const providerId = req.userData.user.userId;
        const holiday = await staffHolidayService.createStaffHoliday({
            ...req.body,
            staffId,
            providerId,
        });

        logger.info('Staff holiday created successfully', { errorId });
        return res.status(201).json({
            success: true,
            message: 'Staff holiday created successfully',
            holiday,
        });
    } catch (error) {
        if (error instanceof InsufficientScopeError) {
            logger.error(`Insufficient scope error: ${error.message}`, {
                errorId,
            });
            return res
                .status(403)
                .json(
                    errorUtil.createErrorResponse(
                        [{ field: 'scope', message: error.message }],
                        errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
                        403,
                        errorId
                    )
                );
        }

        logger.error(`Error creating staff holiday: ${error.message}`, {
            errorId,
        });
        return res
            .status(500)
            .json(
                errorUtil.createErrorResponse(
                    [{ field: 'holiday', message: error.message }],
                    errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                    500,
                    errorId
                )
            );
    }
};

const getAllHolidays = async (req, res) => {
    const errorId = errorUtil.generateErrorId();
    try {
        const userRole = req.userData.user.role;
        const userProviderId = req.userData.user.userId;

        let ProviderId;
        if (userRole === 'admin') {
            ProviderId = req.query.providerId || null;
        } else if (userRole === 'provider') {
            ProviderId = userProviderId;
        }
        logger.info(
            `Fetching holidays for ProviderId: ${ProviderId}, UserRole: ${userRole}`,
            { errorId }
        );
        logger.info('Received request to get holidays', { errorId });

        const {
            page = '1',
            limit = '10',
            search,
            sortBy = 'updatedAt',
            sortOrder = 'desc',
        } = req.query;

        const { pageNum, limitNum, sortDirection } = validateAndParseParams(
            page == 'undefined' ? '1' : page,
            limit,
            sortOrder
        );

        const query = buildQueryFilter(search || '', ProviderId);

        const { holidays, total } = await staffHolidayService.getAllHolidays(
            query,
            sortBy,
            sortDirection,
            pageNum,
            limitNum
        );

        const totalPages = limitNum > 0 ? Math.ceil(total / limitNum) : 0;

        logger.info(
            `Holidays fetched successfully. Total records: ${total}, Total pages: ${totalPages}`
        );

        return res.status(200).json({
            success: true,
            message: 'Holidays fetched successfully',
            holidays,
            total,
            page: pageNum,
            pages: totalPages,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(`Error fetching holidays: ${error.message}`, { errorId });

        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const getHolidayByHolidayId = async (req, res) => {
    const errorId = errorUtil.generateErrorId();
    try {
        const { holidayId } = req.params;
        logger.info(`Fetching holiday with ID: ${holidayId}`, { errorId });
        const holiday =
            await staffHolidayService.getHolidayByHolidayId(holidayId);
        if (!holiday) {
            return res
                .status(404)
                .json(
                    errorUtil.createErrorResponse(
                        [{ field: 'holidayId', message: 'Holiday not found' }],
                        errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                        404,
                        errorId
                    )
                );
        }
        return res.status(200).json({
            success: true,
            message: 'Holiday fetched successfully',
            holiday,
        });
    } catch (error) {
        logger.error(`Error fetching holiday: ${error.message}`, { errorId });
        return res
            .status(500)
            .json(
                errorUtil.createErrorResponse(
                    [{ field: 'holiday', message: error.message }],
                    errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                    500,
                    errorId
                )
            );
    }
};

const getHolidaysByStaffId = async (req, res) => {
    const errorId = errorUtil.generateErrorId();
    try {
        const { staffId } = req.params;
        logger.info(`Fetching holidays for staff ID: ${staffId}`, { errorId });

        const holidays =
            await staffHolidayService.getHolidaysByStaffId(staffId);

        return res.status(200).json({
            success: true,
            message: 'Holidays fetched successfully',
            holidays,
        });
    } catch (error) {
        logger.error(`Error fetching holidays for staffId: ${error.message}`, {
            errorId,
        });
        return res
            .status(500)
            .json(
                errorUtil.createErrorResponse(
                    [{ field: 'staffId', message: error.message }],
                    errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                    500,
                    errorId
                )
            );
    }
};

const updateStaffHoliday = async (req, res) => {
    const errorId = errorUtil.generateErrorId();
    try {
        const { holidayId } = req.params;
        logger.info(`Updating holiday with ID: ${holidayId}`, { errorId });

        const updatedHoliday = await staffHolidayService.updateStaffHoliday(
            holidayId,
            req.body
        );

        if (!updatedHoliday) {
            return res
                .status(404)
                .json(
                    errorUtil.createErrorResponse(
                        [{ field: 'holidayId', message: 'Holiday not found' }],
                        errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                        404,
                        errorId
                    )
                );
        }

        return res.status(200).json({
            success: true,
            message: 'Staff holiday updated successfully',
            holiday: updatedHoliday,
        });
    } catch (error) {
        logger.error(`Error updating staff holiday: ${error.message}`, {
            errorId,
        });
        return res
            .status(500)
            .json(
                errorUtil.createErrorResponse(
                    [{ field: 'holiday', message: error.message }],
                    errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                    500,
                    errorId
                )
            );
    }
};

const deleteStaffHoliday = async (req, res) => {
    const errorId = errorUtil.generateErrorId();
    try {
        const { holidayId } = req.params;
        logger.info(`Deleting holiday with ID: ${holidayId}`, { errorId });

        const deleted = await staffHolidayService.deleteStaffHoliday(holidayId);

        if (!deleted) {
            return res
                .status(404)
                .json(
                    errorUtil.createErrorResponse(
                        [{ field: 'holidayId', message: 'Holiday not found' }],
                        errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                        404,
                        errorId
                    )
                );
        }

        return res.status(200).json({
            success: true,
            message: 'Staff holiday deleted successfully',
        });
    } catch (error) {
        logger.error(`Error deleting staff holiday: ${error.message}`, {
            errorId,
        });
        return res
            .status(500)
            .json(
                errorUtil.createErrorResponse(
                    [{ field: 'holiday', message: error.message }],
                    errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                    500,
                    errorId
                )
            );
    }
};

module.exports = {
    createStaffHoliday,
    getHolidayByHolidayId,
    getHolidaysByStaffId,
    updateStaffHoliday,
    deleteStaffHoliday,
    getAllHolidays,
};
