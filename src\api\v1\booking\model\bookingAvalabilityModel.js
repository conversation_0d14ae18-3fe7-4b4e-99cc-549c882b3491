/* eslint-disable id-length */
const mongoose = require('mongoose');

// Time slot schema definition
const timeSlotSchema = new mongoose.Schema(
    {
        bookingId: {
            type: String,
            default: null,
        },

        referenceCode: {
            type: String,
        },

        timeSlotId: {
            type: String,
        },
        from: {
            type: String,
            required: true,
        },
        to: {
            type: String,
            required: true,
        },
        maxBookings: {
            type: Number,
            default: 1,
            min: 1,
        },
        booked: {
            type: Number,
            default: 0,
            min: 0,
        },

        bookingDate: {
            type: Date,
            default: null,
        },
        bookedStaffId: {
            type: String,
            default: null,
        },
        bookingStatus: {
            type: String,
            enum: ['available', 'booked', 'cancelled'],
            default: 'available',
        },
    },
    { _id: false }
);

// Availability log schema definition
const availabilityLogSchema = new mongoose.Schema(
    {
        serviceId: {
            type: String,
            required: true,
            index: true, // Helps with fast lookup by serviceId
        },
        date: {
            type: Date,
            required: true,
            index: true, // Helps with fast lookup by date
        },
        day: {
            type: String,
            enum: [
                'sunday',
                'monday',
                'tuesday',
                'wednesday',
                'thursday',
                'friday',
                'saturday',
            ],
            required: true,
        },
        available: {
            type: Boolean,
            default: false,
        },
        timeSlots: {
            type: [timeSlotSchema],
            default: [],
        },
    },
    {
        timestamps: true, // Automatically handle createdAt and updatedAt
    }
);

// Ensure that combination of serviceId and date is unique
availabilityLogSchema.index({ serviceId: 1, date: 1 }, { unique: true });

// Pre-save hook to update the updatedAt field
availabilityLogSchema.pre('save', function (next) {
    this.updatedAt = new Date();
    next();
});

// Create and export the model
const AvailabilityLog = mongoose.model(
    'BookingAvailability',
    availabilityLogSchema
);

module.exports = AvailabilityLog;
