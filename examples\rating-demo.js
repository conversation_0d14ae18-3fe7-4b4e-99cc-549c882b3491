/**
 * Rating System Demo
 * 
 * This file demonstrates how to use the new rating endpoints
 * for displaying overall ratings and item-specific ratings.
 */

const axios = require('axios');

// Base URL for the API (adjust as needed)
const BASE_URL = 'http://localhost:3000/api/v1/reviews';

/**
 * Demo: Get overall rating for a service
 */
async function demoOverallRating() {
    console.log('\n=== Overall Rating Demo ===');
    
    try {
        const response = await axios.get(`${BASE_URL}/rating/overall`, {
            params: {
                serviceId: 'SRV_001'
            }
        });

        const { rating } = response.data;
        
        console.log('Service Rating Summary:');
        console.log(`- Total Reviews: ${rating.totalReviews}`);
        console.log(`- Average Rating: ${rating.averageRating}/5.0`);
        console.log(`- Rating Quality: ${rating.ratingQuality}`);
        console.log(`- Recommendation: ${rating.recommendationPercentage}%`);
        
        console.log('\nRating Breakdown:');
        Object.entries(rating.ratingBreakdown).forEach(([stars, data]) => {
            console.log(`  ${stars} stars: ${data.count} reviews (${data.percentage}%)`);
        });
        
    } catch (error) {
        console.error('Error fetching overall rating:', error.message);
    }
}

/**
 * Demo: Get multiple rating summaries
 */
async function demoMultipleRatings() {
    console.log('\n=== Multiple Ratings Demo ===');
    
    try {
        const response = await axios.get(`${BASE_URL}/rating/multiple`, {
            params: {
                serviceIds: 'SRV_001,SRV_002,SRV_003'
            }
        });

        const { ratings } = response.data;
        
        console.log('Service Ratings Summary:');
        ratings.forEach(rating => {
            console.log(`\n${rating.itemId}:`);
            console.log(`  - Reviews: ${rating.totalReviews}`);
            console.log(`  - Average: ${rating.averageRating}/5.0`);
            console.log(`  - Quality: ${rating.ratingQuality}`);
        });
        
    } catch (error) {
        console.error('Error fetching multiple ratings:', error.message);
    }
}

/**
 * Demo: Get provider overall rating
 */
async function demoProviderRating() {
    console.log('\n=== Provider Rating Demo ===');
    
    try {
        const response = await axios.get(`${BASE_URL}/rating/overall`, {
            params: {
                providerId: 'PRV_001'
            }
        });

        const { rating } = response.data;
        
        console.log('Provider Rating Summary:');
        console.log(`- Total Reviews: ${rating.totalReviews}`);
        console.log(`- Average Rating: ${rating.averageRating}/5.0`);
        console.log(`- Rating Quality: ${rating.ratingQuality}`);
        console.log(`- Recommendation: ${rating.recommendationPercentage}%`);
        
    } catch (error) {
        console.error('Error fetching provider rating:', error.message);
    }
}

/**
 * Demo: Display rating stars (UI helper)
 */
function displayRatingStars(rating, maxStars = 5) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = maxStars - fullStars - (hasHalfStar ? 1 : 0);
    
    let stars = '★'.repeat(fullStars);
    if (hasHalfStar) stars += '☆';
    stars += '☆'.repeat(emptyStars);
    
    return `${stars} (${rating}/5.0)`;
}

/**
 * Demo: Rating quality color coding (UI helper)
 */
function getRatingColor(quality) {
    const colors = {
        'Excellent': '\x1b[32m',    // Green
        'Very Good': '\x1b[36m',   // Cyan
        'Good': '\x1b[33m',        // Yellow
        'Average': '\x1b[35m',     // Magenta
        'Below Average': '\x1b[31m', // Red
        'Poor': '\x1b[31m',        // Red
        'No Reviews': '\x1b[37m'   // White
    };
    
    const reset = '\x1b[0m';
    return `${colors[quality] || ''}${quality}${reset}`;
}

/**
 * Demo: Format rating breakdown for display
 */
function formatRatingBreakdown(ratingBreakdown) {
    console.log('\nRating Distribution:');
    
    const maxCount = Math.max(...Object.values(ratingBreakdown).map(r => r.count));
    
    Object.entries(ratingBreakdown)
        .sort(([a], [b]) => parseInt(b) - parseInt(a))
        .forEach(([stars, data]) => {
            const barLength = Math.round((data.count / maxCount) * 20);
            const bar = '█'.repeat(barLength) + '░'.repeat(20 - barLength);
            
            console.log(`  ${stars}★ │${bar}│ ${data.count} (${data.percentage}%)`);
        });
}

/**
 * Main demo function
 */
async function runDemo() {
    console.log('🌟 Rating System Demo');
    console.log('=====================');
    
    // Note: These demos will only work if you have a running server
    // with actual review data. Uncomment the lines below to test:
    
    // await demoOverallRating();
    // await demoMultipleRatings();
    // await demoProviderRating();
    
    // Demo UI helpers
    console.log('\n=== UI Helper Demos ===');
    
    console.log('\nRating Stars Display:');
    console.log('4.8 stars:', displayRatingStars(4.8));
    console.log('3.2 stars:', displayRatingStars(3.2));
    console.log('1.5 stars:', displayRatingStars(1.5));
    
    console.log('\nRating Quality Colors:');
    ['Excellent', 'Very Good', 'Good', 'Average', 'Below Average', 'Poor', 'No Reviews']
        .forEach(quality => {
            console.log(`${quality}: ${getRatingColor(quality)}`);
        });
    
    console.log('\nSample Rating Breakdown:');
    const sampleBreakdown = {
        '5': { count: 45, percentage: 45.0 },
        '4': { count: 30, percentage: 30.0 },
        '3': { count: 15, percentage: 15.0 },
        '2': { count: 7, percentage: 7.0 },
        '1': { count: 3, percentage: 3.0 }
    };
    formatRatingBreakdown(sampleBreakdown);
    
    console.log('\n✅ Demo completed!');
}

// Run the demo
if (require.main === module) {
    runDemo().catch(console.error);
}

module.exports = {
    demoOverallRating,
    demoMultipleRatings,
    demoProviderRating,
    displayRatingStars,
    getRatingColor,
    formatRatingBreakdown
};
