/**
 * Simple test script to verify the review API works with frontend data structure
 * Run with: node src/api/v1/Reviews/test/testReviewAPI.js
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000'; // Adjust to your server URL
const API_ENDPOINT = `${BASE_URL}/api/v1/reviews`;

// Mock auth token (replace with actual token in real testing)
const AUTH_TOKEN = 'Bearer your-test-token-here';

// Test data matching frontend structure
const testReviewData = {
    providerId: 'PROV_123456',
    serviceId: 'SRV_789012',
    bookingId: 'BKG_345678',
    
    // Category ratings (1-4 scale)
    qualityRating: 4,
    timelinessRating: 3,
    communicationRating: 4,
    valueRating: 3,
    
    // Review content
    title: 'Excellent service quality',
    comment: 'The service was outstanding with great attention to detail. Highly recommended for anyone looking for quality work.',
    date: '2024-01-15T10:30:00.000Z',
    
    // Image names from S3 upload
    imageNames: [
        'review-image-1-20240115.jpg',
        'review-image-2-20240115.jpg'
    ]
};

async function testCreateReview() {
    console.log('🧪 Testing Review Creation...');
    console.log('📤 Sending data:', JSON.stringify(testReviewData, null, 2));
    
    try {
        const response = await axios.post(API_ENDPOINT, testReviewData, {
            headers: {
                'Authorization': AUTH_TOKEN,
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ Review created successfully!');
        console.log('📥 Response:', JSON.stringify(response.data, null, 2));
        
        // Verify the response structure
        const review = response.data.review;
        console.log('\n🔍 Verification:');
        console.log(`- Overall Rating: ${review.overallRating} (calculated from categories)`);
        console.log(`- Category Ratings: Q:${review.qualityRating}, T:${review.timelinessRating}, C:${review.communicationRating}, V:${review.valueRating}`);
        console.log(`- Images: ${review.imageNames?.length || 0} images`);
        console.log(`- Review ID: ${review.reviewId}`);
        
        return review.reviewId;
        
    } catch (error) {
        console.error('❌ Error creating review:');
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', JSON.stringify(error.response.data, null, 2));
        } else {
            console.error('Error:', error.message);
        }
        return null;
    }
}

async function testGetReviews() {
    console.log('\n🧪 Testing Review Retrieval...');
    
    try {
        const response = await axios.get(API_ENDPOINT);
        
        console.log('✅ Reviews retrieved successfully!');
        console.log(`📊 Found ${response.data.reviews?.length || 0} reviews`);
        
        if (response.data.reviews && response.data.reviews.length > 0) {
            const review = response.data.reviews[0];
            console.log('\n📋 First Review:');
            console.log(`- Title: ${review.title}`);
            console.log(`- Overall Rating: ${review.overallRating}`);
            console.log(`- Provider: ${review.providerId}`);
            console.log(`- Service: ${review.serviceId}`);
        }
        
    } catch (error) {
        console.error('❌ Error retrieving reviews:');
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', JSON.stringify(error.response.data, null, 2));
        } else {
            console.error('Error:', error.message);
        }
    }
}

async function testValidation() {
    console.log('\n🧪 Testing Validation...');
    
    // Test missing required field
    const invalidData = { ...testReviewData };
    delete invalidData.qualityRating;
    
    try {
        await axios.post(API_ENDPOINT, invalidData, {
            headers: {
                'Authorization': AUTH_TOKEN,
                'Content-Type': 'application/json'
            }
        });
        
        console.log('❌ Validation test failed - should have rejected invalid data');
        
    } catch (error) {
        if (error.response && error.response.status === 422) {
            console.log('✅ Validation working correctly!');
            console.log('📋 Validation errors:', JSON.stringify(error.response.data.errors, null, 2));
        } else {
            console.error('❌ Unexpected error during validation test:', error.message);
        }
    }
}

async function testImageValidation() {
    console.log('\n🧪 Testing Image Validation...');
    
    // Test too many images
    const tooManyImages = {
        ...testReviewData,
        imageNames: ['img1.jpg', 'img2.jpg', 'img3.jpg', 'img4.jpg', 'img5.jpg', 'img6.jpg']
    };
    
    try {
        await axios.post(API_ENDPOINT, tooManyImages, {
            headers: {
                'Authorization': AUTH_TOKEN,
                'Content-Type': 'application/json'
            }
        });
        
        console.log('❌ Image validation test failed - should have rejected too many images');
        
    } catch (error) {
        if (error.response && error.response.status === 422) {
            console.log('✅ Image validation working correctly!');
            const hasMaxImageError = error.response.data.errors.some(err => 
                err.message.includes('Maximum 5 images')
            );
            console.log(`📋 Max images error found: ${hasMaxImageError}`);
        } else {
            console.error('❌ Unexpected error during image validation test:', error.message);
        }
    }
}

async function runTests() {
    console.log('🚀 Starting Review API Tests');
    console.log('=' * 50);
    
    // Test 1: Create Review
    const reviewId = await testCreateReview();
    
    // Test 2: Get Reviews
    await testGetReviews();
    
    // Test 3: Validation
    await testValidation();
    
    // Test 4: Image Validation
    await testImageValidation();
    
    console.log('\n🏁 Tests completed!');
    console.log('=' * 50);
    
    if (reviewId) {
        console.log(`💡 Created review ID: ${reviewId}`);
        console.log('💡 You can now test the frontend integration with this data structure.');
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = {
    testCreateReview,
    testGetReviews,
    testValidation,
    testImageValidation,
    runTests
};
