const mongoose = require('mongoose');

const staffSchema = new mongoose.Schema(
    {
        staffId: {
            type: String,
            required: true,
        },

        providerId: {
            type: String,
            required: true,
            default: 'AID_1',
        },

        providerStaffId: {
            type: String,
            required: true,
        },

        fullName: {
            type: String,
            required: true,
        },

        email: {
            type: String,
            required: true,
        },

        phoneNumber: {
            type: String,
            required: true,
        },

        address: {
            type: String,
        },

        country: {
            type: String,
        },

        state: {
            type: String,
        },

        city: {
            type: String,
        },

        zipCode: {
            type: String,
        },

        description: {
            type: String,
        },

        serviceIds: {
            type: [String],
        },

        status: {
            type: Boolean,
            required: true,
            default: true,
        },

        profileImage: {
            type: String,
        },

        numberOfCompletedServices: {
            type: Number,
            default: 0,
        },
    },
    { timestamps: true }
);

module.exports = mongoose.model('Staff', staffSchema);
