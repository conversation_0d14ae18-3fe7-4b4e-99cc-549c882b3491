/* eslint-disable no-undef */
const StaffService = require('../../../src/api/v1/staff/service/staffService');
const Staff = require('../../../src/api/v1/staff/staffModel');
const Counter = require('../../../src/api/v1/staff/staffCounter');

jest.mock('../../../src/api/v1/staff/staffModel');
jest.mock('../../../src/api/v1/staff/staffCounter');

describe('Staff Service', () => {
    let mockStaffData;

    beforeEach(() => {
        // Initialize mock data for each test
        mockStaffData = {
            fullName: '<PERSON>',
            email: '<EMAIL>',
            phoneNumber: '1234567890',
            address: '123 Main St',
            country: 'USA',
            state: 'California',
            city: 'Los Angeles',
            zipCode: '90001',
            description: 'Staff member in charge of operations.',
            serviceIds: ['SERVICE1', 'SERVICE2'],
            status: true,
        };
        jest.clearAllMocks(); // Clear previous mocks to isolate tests
        Staff.prototype.save = jest.fn();
    });

    describe('createStaff', () => {
        test('should create a new staff member', async () => {
            // Arrange
            const mockId = 2;
            const staffId = `STID_${mockId}`;
            const mockStaff = {
                ...mockStaffData,
                staffId,
            };

            // Mock the behavior of getNextSequence and save
            Counter.getNextSequence.mockResolvedValue(mockId); // Simulate Counter returning 2

            // Mock save to return the mockStaff value
            Staff.prototype.save.mockResolvedValue(mockStaff);

            // Act
            const result = await StaffService.createStaff(mockStaffData);

            // Assert
            expect(Counter.getNextSequence).toHaveBeenCalledTimes(1);
            expect(Staff.prototype.save).toHaveBeenCalled();
            const saveResult = await Staff.prototype.save.mock.results[0].value;

            // Verify that save returned the correct staff
            expect(saveResult).toEqual(mockStaff);

            // Verify the result matches the mockStaff
            expect(result).toEqual(mockStaff);
        });

        test('should throw error if creation fails', async () => {
            // Arrange
            const errorMessage = 'Database error';
            Counter.getNextSequence.mockResolvedValue(1);
            Staff.prototype.save.mockRejectedValue(new Error(errorMessage));

            // Act & Assert
            await expect(
                StaffService.createStaff(mockStaffData)
            ).rejects.toThrow(`Error creating Staff: ${errorMessage}`);
        });
    });

    describe('getStaffById', () => {
        test('should return a staff member by ID', async () => {
            // Arrange
            const staffId = 'STID_1';
            const staff = { staffId, fullName: 'John Doe' };
            Staff.findOne.mockResolvedValue(staff);

            // Act
            const result = await StaffService.getStaffById(staffId);

            // Assert
            expect(Staff.findOne).toHaveBeenCalledWith({ staffId });
            expect(result).toEqual(staff);
        });

        test('should return null if staff not found', async () => {
            // Arrange
            const staffId = 'STID_1';
            Staff.findOne.mockResolvedValue(null);

            // Act
            const result = await StaffService.getStaffById(staffId);

            // Assert
            expect(Staff.findOne).toHaveBeenCalledWith({ staffId });
            expect(result).toBeNull();
        });
    });

    describe('updateStaff', () => {
        test('should update an existing staff member', async () => {
            // Arrange
            const staffId = 'STID_1';
            const updateData = { fullName: 'John Doe Updated' };
            const updatedStaff = { staffId, fullName: 'John Doe Updated' };

            // Mock the methods
            Staff.findOne.mockResolvedValue({ staffId }); // Ensure staff exists
            Staff.findOneAndUpdate.mockResolvedValue(updatedStaff); // Mock the update method

            // Act
            const result = await StaffService.updateStaff(staffId, updateData);

            // Assert
            expect(Staff.findOne).toHaveBeenCalledWith({ staffId });
            expect(Staff.findOneAndUpdate).toHaveBeenCalledWith(
                { staffId },
                { $set: updateData },
                { new: true, runValidators: true }
            );
            expect(result).toEqual(updatedStaff);
        });

        test('should throw error if staff not found', async () => {
            // Arrange
            const staffId = 'STID_1';
            const updateData = { fullName: 'John Doe Updated' };

            Staff.findOne.mockResolvedValue(null); // Staff not found

            // Act & Assert
            await expect(
                StaffService.updateStaff(staffId, updateData)
            ).rejects.toThrow('Staff not found');
        });
    });

    describe('deleteStaff', () => {
        test('should delete an existing staff member', async () => {
            // Arrange
            const staffId = 'STID_1';
            const deletedStaff = { staffId, fullName: 'John Doe' };

            // Mock methods for staff existence and deletion
            Staff.findOne.mockResolvedValue({ staffId });
            Staff.findOneAndDelete.mockResolvedValue(deletedStaff);

            // Act
            const result = await StaffService.deleteStaff(staffId);

            // Assert
            expect(Staff.findOne).toHaveBeenCalledWith({ staffId });
            expect(Staff.findOneAndDelete).toHaveBeenCalledWith({ staffId });
            expect(result).toEqual(deletedStaff);
        });

        test('should throw error if staff not found', async () => {
            // Arrange
            const staffId = 'STID_1';
            Staff.findOne.mockResolvedValue(null); // Staff not found

            // Act & Assert
            await expect(StaffService.deleteStaff(staffId)).rejects.toThrow(
                'Staff not found'
            );
        });
    });
});
