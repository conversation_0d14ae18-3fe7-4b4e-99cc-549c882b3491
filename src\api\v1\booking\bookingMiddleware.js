const { body } = require('express-validator');

const validateBooking = [
    // Required/Optional IDs
    body('customerId')
        .optional()
        .isString()
        .withMessage('customerId must be a string'),
    body('providerId')
        .optional()
        .isString()
        .withMessage('providerId must be a string'),

    // Location
    body('locationId').optional().isString(),

    // Staff
    body('staffId').optional().isString(),

    // Additional Services
    body('additionalServiceIds')
        .optional()
        .isArray()
        .withMessage('additionalServiceIds must be an array'),
    body('additionalServiceIds.*')
        .isString()
        .withMessage('Each additionalServiceId must be a string'),

    // Appointment
    body('appointmentDate')
        .optional()
        .isISO8601()
        .withMessage('appointmentDate must be a valid date')
        .toDate()
        .custom((value) => {
            const now = new Date();
            const twoMonthsLater = new Date();
            twoMonthsLater.setMonth(now.getMonth() + 2);

            if (value > twoMonthsLater) {
                throw new Error(
                    'appointmentDate must be within 2 months from now'
                );
            }

            return true;
        }),
    body('appointmentTimeSlot').optional().isString(),

    // Personal Info
    body('personalInfo.firstName').optional().isString(),
    body('personalInfo.lastName').optional().isString(),
    body('personalInfo.email')
        .optional()
        .isEmail()
        .withMessage('Must be a valid email'),
    body('personalInfo.phone').optional().isString(),
    body('personalInfo.address.street').optional().isString(),
    body('personalInfo.address.city').optional().isString(),
    body('personalInfo.address.state').optional().isString(),
    body('personalInfo.address.postalCode').optional().isString(),
    body('personalInfo.bookingNotes').optional().isString(),

    // // Cart (Required)
    // body('cart')
    //     .isArray({ min: 1 })
    //     .withMessage('cart must be a non-empty array'),
    // body('cart.*.serviceId').optional().isString(),
    // body('cart.*.name').optional().isString(),
    // body('cart.*.duration').optional().isString(),
    // body('cart.*.price')
    //     .optional()
    //     .isNumeric()
    //     .withMessage('Price must be a number'),

    // Payment
    body('paymentMethod')
        .optional()
        .isIn([
            'Wallet',
            'Cash on Delivery',
            'Credit/Debit Card',
            'Stripe',
            'PayPal',
            'Razorpay',
            'PaySolution',
            'Square',
        ])
        .withMessage('Invalid payment method'),
    body('isPaid')
        .optional()
        .isBoolean()
        .withMessage('isPaid must be a boolean'),

    // Booking Info
    body('bookingId').optional().isString(),
    body('bookingStatus')
        .optional()
        .isIn([
            'Pending',
            'Confirmed',
            'Cancelled',
            'Completed',
            'Incomplete',
            'Inprogress',
        ])
        .withMessage('Invalid booking status'),

    // Pricing
    body('subtotal')
        .optional()
        .isNumeric()
        .withMessage('Subtotal must be a number'),
    body('tax').optional().isNumeric().withMessage('Tax must be a number'),
    body('discount')
        .optional()
        .isNumeric()
        .withMessage('Discount must be a number'),
    body('total').optional().isNumeric().withMessage('Total must be a number'),

    // Timestamps
    body('bookingDate').optional().isISO8601().toDate(),
    body('providerAcceptanceDate').optional().isISO8601().toDate(),
    body('completionDate').optional().isISO8601().toDate(),

    // Audit Logs
    body('auditLogs')
        .optional()
        .isArray()
        .withMessage('auditLogs must be an array'),
    body('auditLogs.*.action').optional().isString(),
    body('auditLogs.*.performedBy').optional().isString(),
    body('auditLogs.*.timestamp').optional().isISO8601().toDate(),
];

module.exports = {
    validateBooking,
};
