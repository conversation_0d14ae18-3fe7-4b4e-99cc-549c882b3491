const RatingService = require('../../../src/api/v1/Reviews/service/ratingService');
const Review = require('../../../src/api/v1/Reviews/model/reviewModel');

// Mock the Review model
jest.mock('../../../src/api/v1/Reviews/model/reviewModel');

describe('RatingService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('getQuickRatingSummary', () => {
        it('should return rating summary for a service', async () => {
            const mockAggregateResult = [{
                totalReviews: 10,
                averageRating: 4.2,
                starCounts: {
                    '5': 4,
                    '4': 3,
                    '3': 2,
                    '2': 1,
                    '1': 0
                }
            }];

            Review.aggregate.mockResolvedValue(mockAggregateResult);

            const result = await RatingService.getQuickRatingSummary('SRV_001', 'serviceId');

            expect(Review.aggregate).toHaveBeenCalledWith([
                {
                    $match: {
                        isDeleted: false,
                        status: 'approved',
                        serviceId: 'SRV_001'
                    }
                },
                expect.any(Object),
                expect.any(Object)
            ]);

            expect(result).toEqual({
                totalReviews: 10,
                averageRating: 4.2,
                starCounts: {
                    '5': 4,
                    '4': 3,
                    '3': 2,
                    '2': 1,
                    '1': 0
                },
                ratingQuality: 'Very Good'
            });
        });

        it('should return default values when no reviews exist', async () => {
            Review.aggregate.mockResolvedValue([]);

            const result = await RatingService.getQuickRatingSummary('SRV_002', 'serviceId');

            expect(result).toEqual({
                totalReviews: 0,
                averageRating: 0,
                starCounts: { '5': 0, '4': 0, '3': 0, '2': 0, '1': 0 },
                ratingQuality: 'Poor'
            });
        });

        it('should handle database errors', async () => {
            const error = new Error('Database connection failed');
            Review.aggregate.mockRejectedValue(error);

            await expect(
                RatingService.getQuickRatingSummary('SRV_001', 'serviceId')
            ).rejects.toThrow('Database connection failed');
        });
    });

    describe('getRatingStatistics', () => {
        it('should return comprehensive rating statistics', async () => {
            const mockAggregateResult = [{
                totalReviews: 20,
                averageRating: 4.1,
                highestRating: 5,
                lowestRating: 2,
                totalHelpfulVotes: 45,
                ratingBreakdown: {
                    '5': { count: 8, percentage: 40.0 },
                    '4': { count: 6, percentage: 30.0 },
                    '3': { count: 4, percentage: 20.0 },
                    '2': { count: 2, percentage: 10.0 },
                    '1': { count: 0, percentage: 0.0 }
                }
            }];

            Review.aggregate.mockResolvedValue(mockAggregateResult);

            const filters = { serviceId: 'SRV_001' };
            const result = await RatingService.getRatingStatistics(filters);

            expect(Review.aggregate).toHaveBeenCalled();
            expect(result.totalReviews).toBe(20);
            expect(result.averageRating).toBe(4.1);
            expect(result.ratingQuality).toBe('Very Good');
            expect(result.recommendationPercentage).toBe(70); // 8+6 out of 20
            expect(result.satisfactionScore).toBe(80); // Weighted calculation
        });

        it('should handle date range filters', async () => {
            Review.aggregate.mockResolvedValue([{
                totalReviews: 5,
                averageRating: 4.5,
                highestRating: 5,
                lowestRating: 4,
                totalHelpfulVotes: 12,
                ratingBreakdown: {
                    '5': { count: 3, percentage: 60.0 },
                    '4': { count: 2, percentage: 40.0 },
                    '3': { count: 0, percentage: 0.0 },
                    '2': { count: 0, percentage: 0.0 },
                    '1': { count: 0, percentage: 0.0 }
                }
            }]);

            const filters = {
                serviceId: 'SRV_001',
                startDate: '2024-01-01',
                endDate: '2024-01-31'
            };

            const result = await RatingService.getRatingStatistics(filters);

            expect(result.totalReviews).toBe(5);
            expect(result.averageRating).toBe(4.5);
            expect(result.ratingQuality).toBe('Excellent');
        });
    });

    describe('getRatingQuality', () => {
        it('should return correct quality indicators', () => {
            expect(RatingService.getRatingQuality(4.8)).toBe('Excellent');
            expect(RatingService.getRatingQuality(4.2)).toBe('Very Good');
            expect(RatingService.getRatingQuality(3.7)).toBe('Good');
            expect(RatingService.getRatingQuality(3.2)).toBe('Average');
            expect(RatingService.getRatingQuality(2.5)).toBe('Below Average');
            expect(RatingService.getRatingQuality(1.5)).toBe('Poor');
        });
    });

    describe('getRecommendationPercentage', () => {
        it('should calculate recommendation percentage correctly', () => {
            const ratingBreakdown = {
                '5': { count: 10 },
                '4': { count: 5 },
                '3': { count: 3 },
                '2': { count: 1 },
                '1': { count: 1 }
            };

            const result = RatingService.getRecommendationPercentage(ratingBreakdown);
            expect(result).toBe(75); // (10+5)/(10+5+3+1+1) * 100 = 75%
        });

        it('should return 0 for no ratings', () => {
            const ratingBreakdown = {
                '5': { count: 0 },
                '4': { count: 0 },
                '3': { count: 0 },
                '2': { count: 0 },
                '1': { count: 0 }
            };

            const result = RatingService.getRecommendationPercentage(ratingBreakdown);
            expect(result).toBe(0);
        });
    });

    describe('getSatisfactionScore', () => {
        it('should calculate satisfaction score correctly', () => {
            const ratingBreakdown = {
                '5': { count: 4 }, // 4 * 5 = 20
                '4': { count: 3 }, // 3 * 4 = 12
                '3': { count: 2 }, // 2 * 3 = 6
                '2': { count: 1 }, // 1 * 2 = 2
                '1': { count: 0 }  // 0 * 1 = 0
            };
            // Total weighted: 40, Total ratings: 10, Max possible: 50
            // Score: (40/50) * 100 = 80%

            const result = RatingService.getSatisfactionScore(ratingBreakdown);
            expect(result).toBe(80);
        });

        it('should return 0 for no ratings', () => {
            const ratingBreakdown = {
                '5': { count: 0 },
                '4': { count: 0 },
                '3': { count: 0 },
                '2': { count: 0 },
                '1': { count: 0 }
            };

            const result = RatingService.getSatisfactionScore(ratingBreakdown);
            expect(result).toBe(0);
        });
    });
});
