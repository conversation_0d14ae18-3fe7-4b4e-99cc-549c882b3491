const StaffHoliday = require('../model/staffHolidayModel');

const StaffService = require('../service/staffService');

const Counter = require('../counter/holidayCounter');

const logger = require('../../../common/utils/logger');

const generateHolidayId = async () => {
    const seq = await Counter.getNextSequence();
    return `HID_${seq.toString().padStart(4, '0')}`;
};

const hasOverlappingHoliday = async (
    staffId,
    startDate,
    endDate,
    excludeId = null
) => {
    try {
        const query = {
            staffId,
            startDate: { $lte: endDate },
            endDate: { $gte: startDate },
        };

        if (excludeId) {
            query.holidayId = { $ne: excludeId };
        }

        const conflict = await StaffHoliday.findOne(query);
        return !!conflict;
    } catch (error) {
        logger.error('Error checking overlapping holidays:', error.message);
        throw new Error('Failed to check overlapping holiday');
    }
};

const createStaffHoliday = async (holidayData) => {
    try {
        const { staffId, startDate, endDate } = holidayData;

        const staff = await StaffService.getStaffById(staffId);
        if (!staff) throw new Error('Staff not found');

        const overlap = await hasOverlappingHoliday(
            staffId,
            startDate,
            endDate
        );
        if (overlap) throw new Error('Holiday overlaps with an existing one');

        const holidayId = await generateHolidayId();

        const newHoliday = new StaffHoliday({
            ...holidayData,
            staffName: staff.fullName,
            holidayId,
        });

        return await newHoliday.save();
    } catch (error) {
        logger.error('Error creating holiday:', error.message);
        throw new Error('Failed to create holiday: ' + error.message);
    }
};
const getAllHolidays = async (
    query,
    sortBy,
    sortDirection,
    pageNum,
    limitNum
) => {
    const skip = (pageNum - 1) * limitNum;

    if (![1, -1].includes(sortDirection)) {
        throw new Error(
            'Invalid sort direction. Use 1 for ascending and -1 for descending.'
        );
    }

    try {
        logger.info(`Holiday query: ${JSON.stringify(query)}`);

        const holidays = await StaffHoliday.find(query)
            .sort({ [sortBy]: sortDirection, _id: 1 }) // Add _id for stable sort
            .skip(skip)
            .limit(limitNum);

        const total = await StaffHoliday.countDocuments(query);

        return { holidays, total };
    } catch (error) {
        logger.error('Error fetching holidays:', error.message);
        throw new Error('Failed to retrieve holidays.');
    }
};

const getHolidaysByStaffId = async (staffId) => {
    try {
        return await StaffHoliday.find({ staffId });
    } catch (error) {
        logger.error(
            `Error fetching holidays for staffId: ${staffId}`,
            error.message
        );
        throw new Error('Failed to retrieve holidays for staff');
    }
};

const getHolidayByHolidayId = async (holidayId) => {
    try {
        return await StaffHoliday.findOne({ holidayId });
    } catch (error) {
        logger.error(`Error fetching holidayId: ${holidayId}`, error.message);
        throw new Error('Failed to retrieve holiday');
    }
};

const updateStaffHoliday = async (holidayId, updateData) => {
    try {
        // Fetch existing holiday
        const existing = await getHolidayByHolidayId(holidayId);
        if (!existing) throw new Error('Holiday not found');

        const startDate = updateData.startDate || existing.startDate;
        const endDate = updateData.endDate || existing.endDate;

        // Basic date validation using string comparison
        if (!startDate || !endDate) {
            throw new Error('Start date and end date are required');
        }

        // Check for overlapping holidays (excluding current one)
        const overlap = await hasOverlappingHoliday(
            existing.staffId,
            startDate,
            endDate,
            holidayId
        );
        if (overlap) {
            throw new Error('Updated holiday overlaps with an existing one');
        }

        // Update the holiday
        const updatedHoliday = await StaffHoliday.findOneAndUpdate(
            { holidayId },
            {
                $set: {
                    ...updateData,
                    startDate,
                    endDate,
                },
            },
            {
                new: true,
                runValidators: true,
            }
        );

        if (!updatedHoliday) {
            throw new Error('Failed to update: Holiday not found or invalid');
        }

        return updatedHoliday;
    } catch (error) {
        logger.error(`Error updating holidayId: ${holidayId}`, error.message);
        throw new Error('Failed to update holiday: ' + error.message);
    }
};

const deleteStaffHoliday = async (holidayId) => {
    try {
        return await StaffHoliday.findOneAndDelete({ holidayId });
    } catch (error) {
        logger.error(`Error deleting holidayId: ${holidayId}`, error.message);
        throw new Error('Failed to delete holiday');
    }
};

module.exports = {
    createStaffHoliday,
    getHolidaysByStaffId,
    getHolidayByHolidayId,
    updateStaffHoliday,
    deleteStaffHoliday,
    hasOverlappingHoliday,
    getAllHolidays,
};
