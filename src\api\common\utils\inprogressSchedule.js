const cron = require('node-cron');

const logger = require('./logger');

const bookingService = require('../../v1/booking/service/bookingService');

const inprogressSchedule = () => {
    cron.schedule('* * * * *', async () => {
        try {
            const now = new Date();

            logger.info(
                `Scheduled task running at ${now.toISOString()} - checking for confirmed bookings to update.`
            );

            const query = { bookingStatus: 'confirmed' };
            const sortBy = 'appointmentDate';
            const sortDirection = 1;
            const pageNum = 1;
            const limitNum = 1000;

            const { bookings } = await bookingService.getBookings(
                query,
                sortBy,
                sortDirection,
                pageNum,
                limitNum
            );

            logger.info(
                `Found ${bookings.length} bookings with status 'confirmed'.`
            );

            for (const booking of bookings) {
                const appointmentDate = new Date(booking.appointmentDate);
                const appointmentTimeFrom = new Date(
                    booking.appointmentTimeFrom
                );

                const appointmentStart = new Date(
                    appointmentDate.getFullYear(),
                    appointmentDate.getMonth(),
                    appointmentDate.getDate(),
                    appointmentTimeFrom.getHours(),
                    appointmentTimeFrom.getMinutes()
                );

                const isSameMinute =
                    now.getFullYear() === appointmentStart.getFullYear() &&
                    now.getMonth() === appointmentStart.getMonth() &&
                    now.getDate() === appointmentStart.getDate() &&
                    now.getHours() === appointmentStart.getHours() &&
                    now.getMinutes() === appointmentStart.getMinutes();

                if (isSameMinute) {
                    await bookingService.updateBookingStatus(
                        booking.bookingId,
                        'InProgress',
                        'System auto-update to InProgress',
                        'system'
                    );

                    logger.info(
                        `Booking ${booking.bookingId} status updated to 'InProgress'.`
                    );
                }
            }
        } catch (err) {
            logger.error('Error during scheduled in-progress task:', err);
        }
    });
};

module.exports = { inprogressSchedule };
