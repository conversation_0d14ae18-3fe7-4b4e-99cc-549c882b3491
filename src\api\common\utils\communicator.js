const axios = require('axios');
const dotenv = require('dotenv');

dotenv.config();

const authDomain = process.env.AUTH_DOMAIN;

if (!authDomain) {
    console.error('Error: AUTH_DOMAIN is not defined in the .env file.');
    process.exit(1);
}

const temporaryData = {
    allUsersData: null,
    providerData: null,
    adminData: null,
};

async function fetchDataFromAuthAllUsers(token) {
    try {
        // Validate token before making request
        if (!token || token === '<your_token_here>' || token.trim() === '') {
            throw new Error('Invalid or missing authentication token');
        }
        console.log(token,"Tokemn");
        console.log(`Attempting to fetch user data from: ${authDomain}/v1/auth/verifiedTokenAllUser`);

        const response = await axios.get(
            `${authDomain}/v1/auth/verifiedTokenAllUser`,
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                timeout: 10000, // 10 second timeout
            }
        );

        temporaryData.allUsersData = response.data;
        console.log('Successfully fetched user data from auth service');
    } catch (error) {
        console.error('Error fetching allUsersData from Auth service:', error.message);

        if (error.response) {
            console.error('Auth service response status:', error.response.status);
            console.error('Auth service response data:', error.response.data);
        }

        // Clear cached data on error
        temporaryData.allUsersData = null;
        throw error;
    }
}

async function fetchDataFromAuthProvider(token) {
    try {
        // Validate token before making request
        if (!token || token === '<your_token_here>' || token.trim() === '') {
            throw new Error('Invalid or missing authentication token');
        }

        console.log(`Attempting to fetch provider data from: ${authDomain}/v1/auth/verifiedTokenProvider`);

        const response = await axios.get(
            `${authDomain}/v1/auth/verifiedTokenProvider`,
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                timeout: 10000, // 10 second timeout
            }
        );

        temporaryData.providerData = response.data;
        console.log('Successfully fetched provider data from auth service');
    } catch (error) {
        console.error('Error fetching providerData from Auth service:', error.message);

        if (error.response) {
            console.error('Auth service response status:', error.response.status);
            console.error('Auth service response data:', error.response.data);
        }

        // Clear cached data on error
        temporaryData.providerData = null;
        throw error;
    }
}

async function fetchDataFromAuthAdmin(token) {
    try {
        // Validate token before making request
        if (!token || token === '<your_token_here>' || token.trim() === '') {
            throw new Error('Invalid or missing authentication token');
        }

        console.log(`Attempting to fetch admin data from: ${authDomain}/v1/auth/verifiedTokenAdmin`);

        const response = await axios.get(
            `${authDomain}/v1/auth/verifiedTokenAdmin`,
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                timeout: 10000, // 10 second timeout
            }
        );

        temporaryData.adminData = response.data;
        console.log('Successfully fetched admin data from auth service');
    } catch (error) {
        console.error('Error fetching adminData from Auth service:', error.message);

        if (error.response) {
            console.error('Auth service response status:', error.response.status);
            console.error('Auth service response data:', error.response.data);
        }

        // Clear cached data on error
        temporaryData.adminData = null;
        throw error;
    }
}

// // const fetchAuthDataMiddleware = (type) => {
// //     return async (req, res, next) => {
// //         try {
// //             const token = req.headers.authorization?.split(' ')[1]; // Extract the token from the Authorization header

// //             if (!token) {
// //                 return res.status(401).json({
// //                     error: 'Authorization token is required',
// //                 });
// //             }

// //             // Determine which API to fetch based on the provided `type`
// //             if (!temporaryData[type]) {
// //                 switch (type) {
// //                     case 'allUsersData':
// //                         await fetchDataFromAuthAllUsers(token);
// //                         break;
// //                     case 'providerData':
// //                         await fetchDataFromAuthProvider(token);
// //                         break;
// //                     case 'adminData':
// //                         await fetchDataFromAuthAdmin(token);
// //                         break;
// //                     default:
// //                         throw new Error(`Unknown data type: ${type}`);
// //                 }
// //             }

// //             req.userData = temporaryData[type]; // Attach the data to req.userData
// //             next();
// //         } catch (error) {
// //             console.error(`Error fetching ${type} from Auth service:`, error);
// //             res.status(500).json({
// //                 error: `Failed to fetch data from Auth service for ${type}`,
// //             });
// //         }
// //     };
// // };
// const fetchAuthDataMiddleware = (type) => {
//     return async (req, res, next) => {
//         try {
//             const token = req.headers.authorization?.split(' ')[1];

//             if (!token) {
//                 return res.status(401).json({
//                     error: 'Authorization token is required',
//                 });
//             }

//             if (!temporaryData[type]) {
//                 switch (type) {
//                     case 'allUsersData':
//                         await fetchDataFromAuthAllUsers(token);
//                         break;
//                     case 'providerData':
//                         await fetchDataFromAuthProvider(token);
//                         break;
//                     case 'adminData':
//                         await fetchDataFromAuthAdmin(token);
//                         break;
//                     default:
//                         return res.status(400).json({
//                             error: `Unknown data type: ${type}`,
//                         });
//                 }
//             }

//             req.userData = temporaryData[type];
//             return next();
//         } catch (error) {
//             console.error(`Error fetching ${type} from Auth service:`, error);
//             return res.status(500).json({
//                 error: `Failed to fetch data from Auth service for ${type}`,
//             });
//         }
//     };
// };

// module.exports = {
//     fetchAuthAllDataMiddleware: fetchAuthDataMiddleware('allUsersData'),
//     fetchAuthProviderDataMiddleware: fetchAuthDataMiddleware('providerData'),
//     fetchAuthAdminDataMiddleware: fetchAuthDataMiddleware('adminData'),
// };


// Base request function for each type
const fetchDataFromAuthService = async (endpoint, token) => {
    try {
        const response = await axios.get(`${authDomain}${endpoint}`, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });
        return response.data;
    } catch (error) {
        console.error(`Error fetching data from ${endpoint}:`, error.message);
        throw error;
    }
};

// Middleware factory
const fetchAuthDataMiddleware = (type) => {
    return async (req, res, next) => {
        try {
            // Extract token from Authorization header
            const authHeader = req.headers.authorization;
            const token = authHeader?.split(' ')[1];

            console.log(`Auth middleware called for type: ${type}`);
            console.log(`Authorization header present: ${!!authHeader}`);
            console.log(`Token extracted: ${token ? 'Yes' : 'No'}`);

            if (!token) {
                console.error('No authorization token provided');
                return res.status(401).json({
                    success: false,
                    error: 'Authorization token is required',
                    message: 'Please provide a valid Bearer token in the Authorization header'
                });
            }

            // Check for development mode bypass
            if (process.env.NODE_ENV === 'development' && token === 'dev-bypass-token') {
                console.log('Development mode: bypassing auth service call');
                req.userData = {
                    user: {
                        userId: 'DEV_USER_001',
                        userType: type === 'allUsersData' ? 'customer' :
                                 type === 'providerData' ? 'provider' : 'admin',
                        email: '<EMAIL>'
                    }
                };
                return next();
            }

            // Check if we need to fetch fresh data or if cached data is available
            if (!temporaryData[type]) {
                console.log(`Fetching fresh ${type} from auth service`);

                switch (type) {
                    case 'allUsersData':
                        await fetchDataFromAuthAllUsers(token);
                        break;
                    case 'providerData':
                        await fetchDataFromAuthProvider(token);
                        break;
                    case 'adminData':
                        await fetchDataFromAuthAdmin(token);
                        break;
                    default:
                        console.error(`Unknown data type: ${type}`);
                        return res.status(400).json({
                            success: false,
                            error: `Unknown data type: ${type}`,
                        });
                }
            } else {
                console.log(`Using cached ${type} data`);
            }

            // Attach user data to request
            req.userData = temporaryData[type];

            if (!req.userData) {
                console.error(`No user data available for type: ${type}`);
                return res.status(401).json({
                    success: false,
                    error: 'Authentication failed',
                    message: 'Unable to verify user credentials'
                });
            }

            console.log(`Successfully authenticated user for type: ${type}`);
            return next();

        } catch (error) {
            console.error(`Error in auth middleware for ${type}:`, error.message);

            // Handle specific error types
            if (error.message.includes('Invalid or missing authentication token')) {
                return res.status(401).json({
                    success: false,
                    error: 'Invalid authentication token',
                    message: 'The provided token is invalid or expired'
                });
            }

            if (error.code === 'ERR_BAD_REQUEST' && error.response?.status === 401) {
                return res.status(401).json({
                    success: false,
                    error: 'Authentication failed',
                    message: 'Token verification failed with auth service'
                });
            }

            // Generic server error
            return res.status(500).json({
                success: false,
                error: 'Internal server error',
                message: `Failed to authenticate user: ${error.message}`
            });
        }
    };
};

/**
 * Clear cached authentication data
 * Useful for testing or when tokens expire
 */
const clearAuthCache = () => {
    temporaryData.allUsersData = null;
    temporaryData.providerData = null;
    temporaryData.adminData = null;
    console.log('Authentication cache cleared');
};

/**
 * Get current cached data (for debugging)
 */
const getAuthCache = () => {
    return {
        allUsersData: !!temporaryData.allUsersData,
        providerData: !!temporaryData.providerData,
        adminData: !!temporaryData.adminData,
    };
};

/**
 * Development helper to create mock middleware
 */
const createMockAuthMiddleware = (userType = 'customer') => {
    return (req, res, next) => {
        req.userData = {
            user: {
                userId: `MOCK_${userType.toUpperCase()}_001`,
                userType: userType,
                email: `mock-${userType}@example.com`
            }
        };
        next();
    };
};

module.exports = {
    fetchAuthAllDataMiddleware: fetchAuthDataMiddleware('allUsersData'),
    fetchAuthProviderDataMiddleware: fetchAuthDataMiddleware('providerData'),
    fetchAuthAdminDataMiddleware: fetchAuthDataMiddleware('adminData'),
    clearAuthCache,
    getAuthCache,
    createMockAuthMiddleware,
};
