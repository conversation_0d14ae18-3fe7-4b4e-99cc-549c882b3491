/* eslint-disable id-length */
const { body } = require('express-validator');

const validateStaff = [
    body('fullName')
        .notEmpty()
        .withMessage('Full name is required.')
        .isString()
        .withMessage('Full name must be a string.')
        .trim(),

    body('email')
        .notEmpty()
        .withMessage('Email is required.')
        .isEmail()
        .withMessage('Invalid email format.')
        .normalizeEmail(),

    body('phoneNumber')
        .notEmpty()
        .withMessage('Phone number is required.')
        .isString()
        .withMessage('Phone number must be a string.')
        .trim(),

    body('address')
        .optional()
        .isString()
        .withMessage('Address must be a string.')
        .trim(),

    body('country')
        .optional()
        .isString()
        .withMessage('Country must be a string.')
        .trim(),

    body('state')
        .optional()
        .isString()
        .withMessage('State must be a string.')
        .trim(),

    body('city')
        .optional()
        .isString()
        .withMessage('City must be a string.')
        .trim(),

    body('zipCode')
        .optional()
        .isString()
        .withMessage('Zip code must be a string.')
        .trim(),

    body('description')
        .optional()
        .isString()
        .withMessage('Description must be a string.')
        .trim(),

    body('serviceIds')
        .optional()
        .isArray({ min: 1 })
        .withMessage('At least one service is required.')
        .bail()
        .custom((value) => value.every((v) => typeof v === 'string'))
        .withMessage('Each service must be a string.'),

    body('status')
        .optional()
        .isBoolean()
        .withMessage('Status must be a boolean value.'),
];

module.exports = {
    validateStaff,
};
