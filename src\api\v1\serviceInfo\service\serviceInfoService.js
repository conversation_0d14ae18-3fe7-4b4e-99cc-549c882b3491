/* eslint-disable no-underscore-dangle */
/* eslint-disable id-length */
const ServiceInfo = require('../model/serviceInfoModel');
const dotenv = require('dotenv');
const ServiceInfoCounter = require('../counter/serviceInfoCounter');

const relatedDataService = require('./relatedDataService');

const serviceInfoFetcher = require('./serviceInfoFetcher');

const StaffService = require('../../staff/service/staffService');

const logger = require('../../../common/utils/logger');

const {
    searchDocuments,
    indexDocument,
    updateDocument,
    deleteDocument,
} = require('../../../common/searchfunc/elasticsearchHelpers');

dotenv.config();

// const createServiceInformation = async (serviceData) => {
//     try {
//         const sid = await ServiceInfoCounter.getNextSequence();
//         var serviceId = `SID_${sid}`;

//         const onlineMeetings = [];

//         if (serviceData.allowZoomMeeting && serviceData.zoomInvitationLink) {
//             onlineMeetings.push({
//                 platform: 'Zoom',
//                 invitationLink: serviceData.zoomInvitationLink,
//                 allowMeeting: serviceData.allowZoomMeeting,
//             });
//         }

//         if (serviceData.allowGoogleMeet && serviceData.googleInvitationLink) {
//             onlineMeetings.push({
//                 platform: 'GoogleMeet',
//                 invitationLink: serviceData.googleInvitationLink,
//                 allowMeeting: serviceData.allowGoogleMeet,
//             });
//         }

//         const service = new ServiceInfo({
//             serviceId,
//             serviceTitle: serviceData.serviceTitle,
//             slug: serviceData.slug,
//             categoryId: serviceData.categoryId,
//             subCategoryId: serviceData.subCategoryId,
//             price: serviceData.price,
//             offerPrice: serviceData.offerPrice,
//             isOffers: serviceData.isOffers,
//             priceAfterDiscount: serviceData.priceAfterDiscount,
//             duration: serviceData.duration,
//             onlineMeetings,
//             staff: serviceData.staff,
//             includes: serviceData.includes,
//             serviceOverview: serviceData.serviceOverview,
//             isActive: serviceData.isActive,
//             isAdditional: serviceData.isAdditional,
//             additionalServicesId: [],
//             serviceAvailableId: [],
//             locationId: [],
//             galleryId: [],
//             faqId: [],
//             seoId: [],
//         });

//         const savedService = await service.save();

//         await StaffService.updateStaffServiceIds(service.staff, serviceId);

//         logger.info(`Service created with ID: ${serviceId}`);

//         const relatedData = await relatedDataService.handleRelatedData(
//             serviceId,
//             serviceData
//         );

//         savedService.additionalServicesId = relatedData.additionalServicesId;
//         savedService.serviceAvailableId = relatedData.serviceAvailableIds;
//         savedService.locationId = relatedData.locationIds;
//         savedService.galleryId = relatedData.galleryIds;
//         savedService.faqId = relatedData.faqIds;
//         savedService.seoId = relatedData.seoIds;

//         await savedService.save();
//         logger.info(
//             `Service with ID: ${serviceId} has been updated with related data.`
//         );

//         const finaldataConvert = {
//             ...serviceData,
//             serviceId,
//         };

//         indexDocument(process.env.ELASTIC_INDEX, service._id, finaldataConvert);
//         return savedService;
//     } catch (error) {
//         logger.error(
//             `Error creating service with serviceId: ${serviceId}. Message: ${error.message}`
//         );
//         await deleteServiceInformation(serviceId);
//         throw new Error('Error creating service information: ' + error.message);
//     }
// };

const createServiceInformation = async (serviceData, providerId) => {
    let serviceId;

    try {
        const sid = await ServiceInfoCounter.getNextSequence();
        serviceId = `SID_${sid}`;

        const onlineMeetings = [];

        if (serviceData.allowZoomMeeting && serviceData.zoomInvitationLink) {
            onlineMeetings.push({
                platform: 'Zoom',
                invitationLink: serviceData.zoomInvitationLink,
                allowMeeting: serviceData.allowZoomMeeting,
            });
        }

        if (serviceData.allowGoogleMeet && serviceData.googleInvitationLink) {
            onlineMeetings.push({
                platform: 'GoogleMeet',
                invitationLink: serviceData.googleInvitationLink,
                allowMeeting: serviceData.allowGoogleMeet,
            });
        }

        const service = new ServiceInfo({
            serviceId,
            providerId,
            serviceTitle: serviceData.serviceTitle,
            slug: serviceData.slug,
            categoryId: serviceData.categoryId,
            subCategoryId: serviceData.subCategoryId,
            price: serviceData.price,
            offerPrice: serviceData.offerPrice,
            isOffers: serviceData.isOffers,
            priceAfterDiscount: serviceData.priceAfterDiscount,
            duration: serviceData.duration,
            onlineMeetings,
            staff: serviceData.staff,
            includes: serviceData.includes,
            serviceOverview: serviceData.serviceOverview,
            isActive: serviceData.isActive,
            isAdditional: serviceData.isAdditional,
            additionalServicesId: [],
            serviceAvailableId: [],
            locationId: [],
            galleryId: [],
            faqId: [],
            seoId: [],
        });

        const savedService = await service.save();

        await StaffService.updateStaffServiceIds(service.staff, serviceId);

        logger.info(`Service created with ID: ${serviceId}`);

        const relatedData = await relatedDataService.handleRelatedData(
            serviceId,
            serviceData
        );

        savedService.additionalServicesId =
            relatedData.additionalServicesId || [];
        savedService.serviceAvailableId = relatedData.serviceAvailableIds || [];
        savedService.locationId = relatedData.locationIds || [];
        savedService.galleryId = relatedData.galleryIds || [];
        savedService.faqId = relatedData.faqIds || [];
        savedService.seoId = relatedData.seoIds || [];

        await savedService.save();
        logger.info(
            `Service with ID: ${serviceId} has been updated with related data.`
        );

        const finaldataConvert = {
            ...serviceData,
            serviceId,
        };

        indexDocument(process.env.ELASTIC_INDEX, service._id, finaldataConvert);

        return savedService;
    } catch (error) {
        logger.error(
            `Error creating service with serviceId: ${serviceId}. Message: ${error.message}`
        );

        try {
            await deleteServiceInformation(serviceId);
            logger.info(
                `Service with serviceId: ${serviceId} has been deleted due to error.`
            );
        } catch (deleteError) {
            logger.error(
                `Error deleting service with serviceId: ${serviceId} during cleanup. Message: ${deleteError.message}`
            );
        }

        throw new Error('Error creating service information: ' + error.message);
    }
};

const getServiceInformation = async (
    query,
    sortBy,
    sortDirection,
    pageNum,
    limitNum
) => {
    const skip = (pageNum - 1) * limitNum;

    if (![1, -1].includes(sortDirection)) {
        throw new Error(
            'Invalid sort direction. Use 1 for ascending and -1 for descending.'
        );
    }

    try {
        const services = await ServiceInfo.find(query)
            .sort({ updatedAt: -1, [sortBy]: sortDirection })
            .skip(skip)
            .limit(limitNum);

        const total = await ServiceInfo.countDocuments(query);

        const serviceIds = services.map((service) => service.serviceId);

        const allServiceData = await getAllServiceData(serviceIds);

        return { services: allServiceData, total };
    } catch (error) {
        logger.error('Error fetching services with details:', error);
        throw new Error('Failed to retrieve services with details.');
    }
};

const getServiceInformationById = async (serviceId) => {
    try {
        const service = await ServiceInfo.findOne({ serviceId });
        if (!service) {
            logger.warn(`Service with ID: ${serviceId} not found.`);
            throw new Error('Service not found.');
        }

        const availability =
            await serviceInfoFetcher.fetchServiceAvailability(service);

        const formattedAvailability = formatAvailability(availability);

        const fullServiceData = {
            _id: service._id,
            serviceId: service.serviceId,
            providerId: service.providerId,
            serviceTitle: service.serviceTitle,
            slug: service.slug,
            categoryId: service.categoryId,
            subCategoryId: service.subCategoryId,
            price: service.price,
            isOffers: service.isOffers,
            offerPrice: service.offerPrice,
            priceAfterDiscount: service.priceAfterDiscount,
            duration: service.duration,
            allowZoomMeeting: service.allowZoomMeeting,
            zoomInvitationLink: service.zoomInvitationLink,
            allowGoogleMeet: service.allowGoogleMeet,
            googleInvitationLink: service.googleInvitationLink,
            staff: service.staff,
            includes: service.includes,
            serviceOverview: service.serviceOverview,
            isActive: service.isActive,
            isAdditional: service.isAdditional,

            additionalServices:
                await serviceInfoFetcher.fetchAdditionalServices(service),
            availability: formattedAvailability.availability,
            location: await serviceInfoFetcher.fetchLocation(service),
            gallery: await serviceInfoFetcher.fetchGallery(service),
            faq: await serviceInfoFetcher.fetchFaq(service),
            seo: await serviceInfoFetcher.fetchSeo(service),
        };

        logger.info(`Fetched full service data for service ID: ${serviceId}`);
        return fullServiceData;
    } catch (error) {
        logger.error(
            `Error retrieving service by ID: ${serviceId} - ${error.message}`
        );
        throw error;
    }
};

const getAllServiceInformation = async (query) => {
    const searchResults = await searchDocuments(
        process.env.ELASTIC_INDEX,
        query
    );
    logger.info(`Search results: ${searchResults}`);
    const totalRecords = searchResults.hits.total.value;
    const finalResults = searchResults.hits.hits;
    const sortedResults = finalResults.map((result) => result._source);
    // const sortedResults = searchResults.sort((a, b) => b._source.rating - a._source.rating);

    return { sortedResults, totalRecords };
};

const updateServiceInformation = async (serviceId, updatedData) => {
    try {
        const service = await ServiceInfo.findOne({ serviceId });
        if (!service) {
            logger.warn(`Service with ID: ${serviceId} not found.`);
            throw new Error('Service not found');
        }

        const updatedFields = {};
        let hasChanges = false;

        const updateFieldIfChanged = (field, newValue) => {
            // Check if newValue is explicitly provided and different from the current value.
            if (newValue !== undefined && newValue !== service[field]) {
                updatedFields[field] = newValue;
                hasChanges = true;
            }
        };

        updateFieldIfChanged('serviceTitle', updatedData.serviceTitle);
        updateFieldIfChanged('slug', updatedData.slug);
        updateFieldIfChanged('categoryId', updatedData.categoryId);
        updateFieldIfChanged('subCategoryId', updatedData.subCategoryId);
        updateFieldIfChanged('price', updatedData.price);
        updateFieldIfChanged('offerPrice', updatedData.offerPrice);
        updateFieldIfChanged(
            'priceAfterDiscount',
            updatedData.priceAfterDiscount
        );
        updateFieldIfChanged('duration', updatedData.duration);
        updateFieldIfChanged('staff', updatedData.staff);
        updateFieldIfChanged('includes', updatedData.includes);
        updateFieldIfChanged('serviceOverview', updatedData.serviceOverview);
        updateFieldIfChanged('isActive', updatedData.isActive);
        updateFieldIfChanged('isOffers', updatedData.isOffers);
        updateFieldIfChanged('isAdditional', updatedData.isAdditional);

        const onlineMeetingsUpdate = [];

        if (updatedData.allowZoomMeeting && updatedData.zoomInvitationLink) {
            onlineMeetingsUpdate.push({
                platform: 'Zoom',
                invitationLink: updatedData.zoomInvitationLink,
                allowMeeting: updatedData.allowZoomMeeting,
            });
        }

        if (updatedData.allowGoogleMeet && updatedData.googleInvitationLink) {
            onlineMeetingsUpdate.push({
                platform: 'GoogleMeet',
                invitationLink: updatedData.googleInvitationLink,
                allowMeeting: updatedData.allowGoogleMeet,
            });
        }

        if (onlineMeetingsUpdate.length > 0) {
            updatedFields['onlineMeetings'] = onlineMeetingsUpdate;
            hasChanges = true;
        }

        const relatedDataUpdates = [];
        await serviceInfoFetcher.updateRelatedData(
            'additionalServicesId',
            relatedDataService.saveAdditionalService,
            updatedData.additionalService,
            serviceId,
            updatedFields,
            relatedDataUpdates
        );
        await serviceInfoFetcher.updateRelatedData(
            'serviceAvailableId',
            relatedDataService.saveAvailability,
            updatedData.availability,
            serviceId,
            updatedFields,
            relatedDataUpdates
        );
        await serviceInfoFetcher.updateRelatedData(
            'locationId',
            relatedDataService.saveLocations,
            updatedData.location,
            serviceId,
            updatedFields,
            relatedDataUpdates
        );
        await serviceInfoFetcher.updateRelatedData(
            'galleryId',
            relatedDataService.saveGallery,
            updatedData.gallery,
            serviceId,
            updatedFields,
            relatedDataUpdates
        );
        await serviceInfoFetcher.updateRelatedData(
            'faqId',
            relatedDataService.saveFaq,
            updatedData.faq,
            serviceId,
            updatedFields,
            relatedDataUpdates
        );
        await serviceInfoFetcher.updateRelatedData(
            'seoId',
            relatedDataService.saveSEO,
            updatedData.seo,
            serviceId,
            updatedFields,
            relatedDataUpdates
        );

        if (hasChanges || relatedDataUpdates.length > 0) {
            const updatedService = await ServiceInfo.findOneAndUpdate(
                { serviceId },
                { $set: updatedFields },
                { new: true }
            );

            if (updatedService) {
                logger.info(
                    `Service with ID: ${serviceId} updated successfully.`
                );
                if (hasChanges) {
                    logger.info(
                        `Updated fields: ${Object.keys(updatedFields).join(', ')}`
                    );
                }
                if (relatedDataUpdates.length > 0) {
                    logger.info(
                        `Updated related data: ${relatedDataUpdates.join(', ')}`
                    );
                }

                const result = await updateDocument(
                    process.env.ELASTIC_INDEX,
                    service._id,
                    updatedFields
                );

                if (result.result !== 'updated') {
                    logger.warn(
                        `Service with ID: ${serviceId} was not updated in Elasticsearch.`
                    );
                }

                logger.info(
                    `Service with ID: ${serviceId} updated in Elasticsearch.`
                );

                return updatedService;
            } else {
                logger.warn(`Service with ID: ${serviceId} was not updated.`);
                throw new Error('Failed to update service.');
            }
        } else {
            logger.info(
                `No changes detected for service with ID: ${serviceId}`
            );
            return service;
        }
    } catch (error) {
        logger.error(
            `Error updating service with ID: ${serviceId} - ${error.message}`
        );
        throw new Error(error.message);
    }
};

const deleteServiceInformation = async (serviceId) => {
    if (!serviceId) {
        logger.error('Service ID is required.');
        throw new Error('Service ID is required.');
    }

    try {
        const service = await ServiceInfo.findOne({ serviceId });

        if (!service) {
            logger.warn(`Service with ID: ${serviceId} not found.`);
            throw new Error('Service not found.');
        }

        const {
            additionalServicesId,
            serviceAvailableId,
            locationId,
            galleryId,
            faqId,
            seoId,
        } = service;

        const deletePromises = [];

        if (additionalServicesId && additionalServicesId.length > 0) {
            deletePromises.push(
                relatedDataService.deleteAdditionalService(additionalServicesId)
            );
        }
        if (serviceAvailableId && serviceAvailableId.length > 0) {
            deletePromises.push(
                relatedDataService.deleteAvailability(serviceAvailableId)
            );
        }
        if (locationId && locationId.length > 0) {
            deletePromises.push(relatedDataService.deleteLocations(locationId));
        }
        if (galleryId && galleryId.length > 0) {
            deletePromises.push(relatedDataService.deleteGallery(galleryId));
        }
        if (galleryId && galleryId.length > 0) {
            deletePromises.push(relatedDataService.deleteFaq(faqId));
        }
        if (seoId && seoId.length > 0) {
            deletePromises.push(relatedDataService.deleteSEO(seoId));
        }

        await Promise.all(deletePromises);
        logger.info(
            `Related data for service ID: ${serviceId} has been deleted.`
        );

        await StaffService.removeServiceIdFromStaff(service.staff, serviceId);

        const deletedService = await ServiceInfo.findOneAndDelete({
            serviceId,
        });

        if (!deletedService) {
            logger.warn(`Service with ID: ${serviceId} was not deleted.`);
            throw new Error('Service not deleted.');
        }
        const result = await deleteDocument(
            process.env.ELASTIC_INDEX,
            service._id
        );
        if (result.result !== 'deleted') {
            logger.warn(
                `Service with ID: ${serviceId} was not deleted from Elasticsearch.`
            );
        }

        logger.info(
            `Service with ID: ${serviceId} has been deleted from Elasticsearch.`
        );
        logger.info(`Service with ID: ${serviceId} has been deleted.`);

        return deletedService;
    } catch (error) {
        logger.error(
            `Error deleting service with ID: ${serviceId} - ${error.message}`
        );
        throw new Error(error.message);
    }
};

const getAllServiceData = async (serviceIds) => {
    try {
        const serviceDataPromises = serviceIds.map((serviceId) =>
            getServiceInformationById(serviceId)
        );
        const services = await Promise.all(serviceDataPromises);

        return services;
    } catch (error) {
        logger.error(`Error fetching all service data: ${error.message}`);
        throw new Error('Failed to retrieve all service data.');
    }
};

async function checkAndAddStaff(existingStaffIds, newStaffIds, serviceId) {
    for (const newStaffId of newStaffIds) {
        if (!existingStaffIds.includes(newStaffId)) {
            await StaffService.updateStaffServiceIds(newStaffId, serviceId);
            logger.info(`New staff ID ${newStaffId} can be added.`);
        }
    }

    for (const existingStaffId of existingStaffIds) {
        if (!newStaffIds.includes(existingStaffId)) {
            await StaffService.removeServiceIdFromStaff(
                existingStaffId,
                serviceId
            );
            logger.warn(
                `Staff with ID ${existingStaffId} is no longer included in the new list.`
            );
        }
    }
}

// const formatAvailability = (availability) => {
//     const formattedAvailability = {
//         Monday: [],
//         Tuesday: [],
//         Wednesday: [],
//         Thursday: [],
//         Friday: [],
//         Saturday: [],
//         Sunday: [],
//     };

//     let alldate = true;
//     let referenceSlots = null;

//     const compareTimeSlots = (slots1, slots2) => {
//         if (slots1.length !== slots2.length) return false;
//         return slots1.every((slot, index) => {
//             return (
//                 slot.from === slots2[index].from &&
//                 slot.to === slots2[index].to &&
//                 slot.maxBookings === slots2[index].maxBookings
//             );
//         });
//     };

//     availability.forEach((item) => {
//         const dayName = item.day.charAt(0).toUpperCase() + item.day.slice(1);

//         if (formattedAvailability[dayName] && Array.isArray(item.timeSlots)) {
//             if (referenceSlots === null) {
//                 referenceSlots = item.timeSlots;
//             }

//             item.timeSlots.forEach((slot) => {
//                 formattedAvailability[dayName].push({
//                     id: item.id,
//                     from: slot.from,
//                     to: slot.to,
//                     slots: slot.maxBookings,
//                     availableSlots: slot.availableSlots,
//                     booked: slot.booked,
//                 });
//             });

//             // Only use this block if bookingTimeSlots actually exist in your data

//             if (!compareTimeSlots(referenceSlots, item.timeSlots)) {
//                 alldate = false;
//             }
//         } else {
//             console.warn(
//                 `Missing or malformed timeSlots for item with ID: ${item.id}`
//             );
//         }
//     });

//     const allDaysHaveSlots = Object.values(formattedAvailability).every(
//         (day) => day.length > 0
//     );
//     if (!allDaysHaveSlots) {
//         alldate = false;
//     }

//     const updatedAvailability = availability.map((item) => ({
//         ...item,
//         alldate: alldate,
//     }));

//     return {
//         availability: updatedAvailability,
//         formattedAvailability,
//     };
// };
const formatAvailability = (availability) => {
    const daysOfWeek = [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday',
    ];

    const formattedAvailability = Object.fromEntries(
        daysOfWeek.map((day) => [day, []])
    );

    let alldate = true;
    let referenceSlots = null;

    const compareTimeSlots = (slots1, slots2) => {
        if (slots1.length !== slots2.length) return false;
        return slots1.every(
            (slot, i) =>
                slot.from === slots2[i].from &&
                slot.to === slots2[i].to &&
                slot.maxBookings === slots2[i].maxBookings
        );
    };

    const transformedAvailability = availability.map((item) => {
        const slots = item.timeSlots;

        // If referenceSlots is null, assign first item's slots to compare with others
        if (referenceSlots === null) referenceSlots = slots;

        // Compare current slots with referenceSlots, update alldate accordingly
        if (!compareTimeSlots(referenceSlots, slots)) alldate = false;

        // Compose combined time slot (from first slot's start to last slot's end)
        const combinedSlot = {
            from: slots[0]?.from || '',
            to: slots[slots.length - 1]?.to || '',
            maxBookings: slots.length,
            availableSlots: slots.reduce(
                (sum, s) => sum + (s.availableSlots || 0),
                0
            ),
            booked: slots.reduce((sum, s) => sum + (s.booked || 0), 0),
        };

        // Format day name properly
        const dayName =
            item.day.charAt(0).toUpperCase() + item.day.slice(1).toLowerCase();

        // Add combined slot to formattedAvailability
        if (formattedAvailability[dayName]) {
            formattedAvailability[dayName].push({
                id: item.id,
                ...combinedSlot,
            });
        } else {
            console.warn(`Day ${dayName} is invalid or not recognized.`);
        }

        // Return transformed entry with a single combined slot
        return {
            id: item.id,
            day: item.day,
            timeSlots: [combinedSlot],
        };
    });

    // Check if every day has at least one slot; if not, alldate is false
    const allDaysHaveSlots = Object.values(formattedAvailability).every(
        (slots) => slots.length > 0
    );
    if (!allDaysHaveSlots) alldate = false;

    // Add alldate flag to all transformed availability entries
    const updatedAvailability = transformedAvailability.map((item) => ({
        ...item,
        alldate,
    }));

    return {
        availability: updatedAvailability,
        formattedAvailability,
    };
};

module.exports = {
    createServiceInformation,
    getServiceInformation,
    getServiceInformationById,
    updateServiceInformation,
    getAllServiceInformation,
    deleteServiceInformation,
    checkAndAddStaff,
    formatAvailability,
};
