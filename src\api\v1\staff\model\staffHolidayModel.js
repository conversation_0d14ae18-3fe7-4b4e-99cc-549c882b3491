const mongoose = require('mongoose');

const StaffHolidaySchema = new mongoose.Schema(
    {
        holidayId: {
            type: String,
            required: true,
        },

        staffId: {
            type: String,
            required: true,
        },
        staffName: {
            type: String,
            required: true,
        },
        providerId: {
            type: String,
            required: true,
        },

        startDate: {
            type: Date,
            required: true,
        },

        endDate: {
            type: Date,
            required: true,
        },
        reason: {
            type: String,

            default: 'Other',
        },
        approved: {
            type: Boolean,
            default: true,
        },
        notes: {
            type: String,
        },
    },
    { timestamps: true }
);

module.exports = mongoose.model('StaffHoliday', StaffHolidaySchema);
