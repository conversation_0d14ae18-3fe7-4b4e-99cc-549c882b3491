/* eslint-disable id-length */
const { validationResult } = require('express-validator');

const { InsufficientScopeError } = require('express-oauth2-jwt-bearer');

const BookingService = require('./service/bookingService');

const AvailableTimeSlot = require('./service/bookingAvalabilityService');

const {
    buildQueryFilter,
    validateAndParseParams,
} = require('../../common/utils/filter');

const logger = require('../../common/utils/logger');

const errorUtil = require('../../common/utils/error');

// Create Booking
const createBooking = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const formattedErrors = errors.array().map((err) => ({
                field: err.path,
                message: err.msg,
            }));

            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                formattedErrors,
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                422,
                errorId
            );

            logger.error(
                `Validation error for request: ${JSON.stringify(formattedErrors)}`,
                { errorId }
            );

            return res.status(422).json(errorResponse);
        }

        const customerId = req.userData.user.userId;
        const booking = await BookingService.createBooking(
            req.body,
            customerId
        );

        logger.info(`Booking created successfully. Ref: ${booking.bookingId}`);

        return res.status(201).json({
            success: true,
            message: 'Booking created successfully',
            booking,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();

        if (err instanceof InsufficientScopeError) {
            logger.error(`Booking Insufficient scope error: ${err.message}`, {
                errorId,
            });

            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'booking scope', message: err.message }],
                errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
                403,
                errorId
            );

            return res.status(403).json(errorResponse);
        }

        logger.error(`Error creating booking: ${err.message}`, { errorId });

        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'booking', message: err.message }],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
};

// Get Bookings (Paginated)
const getBookings = async (req, res) => {
    try {
        const userRole = req.userData.user.role;
        const userId = req.userData.user.userId;

        let providerId = null;
        let customerId = null;

        // Determine filtering logic based on role
        if (userRole === 'admin') {
            // Admin can query by providerId (optional)
            providerId = req.query.providerId || null;
        } else if (userRole === 'provider') {
            providerId = userId;
        } else {
            customerId = userId;
        }

        logger.info(
            `Fetching bookings | role: ${userRole}, providerId: ${providerId}, customerId: ${customerId}`
        );

        const {
            page = '1',
            limit = '10',
            search,
            sortBy = 'updatedAt',
            sortOrder = 'desc',
        } = req.query;

        const { pageNum, limitNum, sortDirection } = validateAndParseParams(
            page,
            limit,
            sortOrder
        );

        // Only one of providerId or customerId is passed into the query builder
        const query = buildQueryFilter(search, providerId, customerId);

        logger.debug(`Booking Query: ${JSON.stringify(query)}`);

        const { bookings, total } = await BookingService.getBookings(
            query,
            sortBy,
            sortDirection,
            pageNum,
            limitNum
        );

        const totalPages = limitNum > 0 ? Math.ceil(total / limitNum) : 0;

        return res.status(200).json({
            success: true,
            message: 'Bookings fetched successfully',
            bookings,
            total,
            page: pageNum,
            pages: totalPages,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error(`Error fetching bookings: ${err.message}`, { errorId });

        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

// Get Booking by Ref
const getBookingById = async (req, res) => {
    try {
        const { bookingId } = req.params;
        const booking = await BookingService.getBookingById(bookingId);

        if (!booking) {
            const errorId = errorUtil.generateErrorId();
            logger.warn(`Booking with Ref ${bookingId} not found`, {
                errorId,
            });

            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'bookingId', message: 'Booking not found' }],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );

            return res.status(404).json(errorResponse);
        }

        logger.info(`Fetched booking with Ref: ${bookingId}`);
        return res.status(200).json({
            success: true,
            message: 'Booking fetched successfully',
            booking,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error fetching booking ${req.params.bookingId}: ${err.message}`,
            {
                errorId,
            }
        );

        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const getBookingByReferenceCode = async (req, res) => {
    try {
        const { referenceCode } = req.params;
        const booking =
            await BookingService.getBookingByReferenceCode(referenceCode);

        if (!booking) {
            const errorId = errorUtil.generateErrorId();
            logger.warn(`Booking with Ref ${referenceCode} not found`, {
                errorId,
            });

            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'referenceCode', message: 'Booking not found' }],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );

            return res.status(404).json(errorResponse);
        }

        logger.info(`Fetched booking with Ref: ${referenceCode}`);
        return res.status(200).json({
            success: true,
            message: 'Booking fetched successfully',
            booking,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error fetching booking ${req.params.bookingId}: ${err.message}`,
            {
                errorId,
            }
        );

        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

// Update Booking
const updateBooking = async (req, res) => {
    try {
        const { bookingId } = req.params;
        const existingBooking = await BookingService.getBookingById(bookingId);

        if (!existingBooking) {
            const errorId = errorUtil.generateErrorId();
            logger.warn(`Booking with Ref ${bookingId} not found for update`, {
                errorId,
            });

            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: 'bookingId',
                        message: 'Booking not found for update',
                    },
                ],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );

            return res.status(404).json(errorResponse);
        }

        const { role, userId: currentUserId } = req.userData?.user || {};

        const isAdmin = role === 'admin';
        const isOwner = existingBooking.customerId === currentUserId;

        if (!isAdmin && !isOwner) {
            const errorId = errorUtil.generateErrorId();
            logger.warn('Permission denied for booking update', {
                errorId,
                bookingId,
                role,
                currentUserId,
            });

            return res.status(403).json(
                errorUtil.createErrorResponse(
                    [
                        {
                            field: 'permissions',
                            message: 'Access denied to update this booking',
                        },
                    ],
                    errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
                    403,
                    errorId
                )
            );
        }

        const updatedBooking = await BookingService.updateBooking(
            bookingId,
            req.body
        );

        logger.info(`Booking with Ref ${bookingId} updated successfully`);

        return res.status(200).json({
            success: true,
            message: 'Booking updated successfully',
            booking: updatedBooking,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error updating booking ${req.params.bookingId}: ${err.message}`,
            { errorId }
        );

        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
};

const updateBookingStatus = async (req, res) => {
    const { bookingId } = req.params;
    const { newStatus, note } = req.body;

    try {
        const booking = await BookingService.getBookingById(bookingId);

        if (!booking) {
            const errorId = errorUtil.generateErrorId();
            logger.warn(
                `Booking with Ref ${bookingId} not found for status update`,
                { errorId }
            );

            return res.status(404).json(
                errorUtil.createErrorResponse(
                    [
                        {
                            field: 'bookingId',
                            message: 'Booking not found',
                        },
                    ],
                    errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                    404,
                    errorId
                )
            );
        }

        const { role, userId: currentUserId } = req.userData?.user || {};
        const isAdmin = role === 'admin';
        const isCustomer = booking.customerId === currentUserId;
        const isProvider = booking.providerId === currentUserId;
        const performedBy = currentUserId;

        if (!isAdmin && !isCustomer && !isProvider) {
            const errorId = errorUtil.generateErrorId();
            logger.warn('Permission denied for booking status update', {
                errorId,
                bookingId,
                role,
                currentUserId,
            });

            return res.status(403).json(
                errorUtil.createErrorResponse(
                    [
                        {
                            field: 'permissions',
                            message:
                                'Access denied to update this booking status',
                        },
                    ],
                    errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
                    403,
                    errorId
                )
            );
        }

        const updatedBooking = await BookingService.updateBookingStatus(
            bookingId,
            newStatus,
            note,
            performedBy
        );

        logger.info(
            `Booking status for Ref ${bookingId} updated to ${newStatus} by ${performedBy}`
        );

        return res.status(200).json({
            success: true,
            message: 'Booking status updated',
            booking: updatedBooking,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error updating booking status for Ref ${bookingId}: ${error.message}`,
            { errorId }
        );

        return res.status(400).json(
            errorUtil.createErrorResponse(
                [
                    {
                        field: 'bookingId',
                        message: error.message,
                    },
                ],
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                400,
                errorId
            )
        );
    }
};

const rescheduleBooking = async (req, res) => {
    const { bookingId } = req.params;
    const { staffId, newDate, newFrom, newTo, note } = req.body;

    try {
        const booking = await BookingService.getBookingById(bookingId);

        if (!booking) {
            const errorId = errorUtil.generateErrorId();
            logger.warn(
                `Booking with Ref ${bookingId} not found for rescheduling`,
                { errorId }
            );

            return res.status(404).json(
                errorUtil.createErrorResponse(
                    [
                        {
                            field: 'bookingId',
                            message: 'Booking not found',
                        },
                    ],
                    errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                    404,
                    errorId
                )
            );
        }

        const { role, userId: currentUserId } = req.userData?.user || {};
        const isAdmin = role === 'admin';
        const isCustomer = booking.customerId === currentUserId;
        const isProvider = booking.providerId === currentUserId;
        const performedBy = currentUserId;

        if (!isAdmin && !isCustomer && !isProvider) {
            const errorId = errorUtil.generateErrorId();
            logger.warn('Permission denied for rescheduling booking', {
                errorId,
                bookingId,
                role,
                currentUserId,
            });

            return res.status(403).json(
                errorUtil.createErrorResponse(
                    [
                        {
                            field: 'permissions',
                            message: 'Access denied to reschedule this booking',
                        },
                    ],
                    errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
                    403,
                    errorId
                )
            );
        }

        if (!newDate || !newFrom || !newTo) {
            const errorId = errorUtil.generateErrorId();
            return res.status(400).json(
                errorUtil.createErrorResponse(
                    [
                        {
                            field: 'rescheduleData',
                            message:
                                'Missing required fields: newDate, newFrom, newTo',
                        },
                    ],
                    errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                    400,
                    errorId
                )
            );
        }

        const updatedBooking = await BookingService.rescheduleBooking(
            bookingId,
            staffId,
            newDate,
            newFrom,
            newTo,
            note,
            performedBy
        );

        // logger.info(
        //     `Booking Ref ${bookingId} rescheduled to ${newDate} ${newFrom}-${newTo}  staffId ${staffId} by ${performedBy}`
        // );

        return res.status(200).json({
            success: true,
            message: 'Booking rescheduled successfully',
            booking: updatedBooking,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error rescheduling booking Ref ${bookingId}: ${error.message}`,
            {
                errorId,
            }
        );

        return res.status(400).json(
            errorUtil.createErrorResponse(
                [
                    {
                        field: 'bookingId',
                        message: error.message,
                    },
                ],
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                400,
                errorId
            )
        );
    }
};

const deleteBooking = async (req, res) => {
    try {
        const { bookingId } = req.params;
        const booking = await BookingService.getBookingById(bookingId);

        if (!booking) {
            const errorId = errorUtil.generateErrorId();
            logger.warn(
                `Booking with Ref ${bookingId} not found for deletion`,
                { errorId }
            );

            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: 'bookingId',
                        message: 'Booking not found for deletion',
                    },
                ],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );

            return res.status(404).json(errorResponse);
        }

        const { role, userId: currentUserId } = req.userData?.user || {};
        const isAdmin = role === 'admin';
        const isOwner = booking.customerId === currentUserId;

        if (!isAdmin && !isOwner) {
            const errorId = errorUtil.generateErrorId();
            logger.warn('Permission denied for booking deletion', {
                errorId,
                bookingId,
                role,
                currentUserId,
            });

            return res.status(403).json(
                errorUtil.createErrorResponse(
                    [
                        {
                            field: 'permissions',
                            message: 'Access denied to delete this booking',
                        },
                    ],
                    errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
                    403,
                    errorId
                )
            );
        }

        await BookingService.deleteBooking(bookingId);

        logger.info(`Booking with Ref ${bookingId} deleted successfully`);

        return res.status(200).json({
            success: true,
            message: 'Booking deleted successfully',
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error deleting booking ${req.params.bookingId}: ${err.message}`,
            { errorId }
        );

        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
};

async function checkAvailabilityByService(req, res) {
    try {
        const { serviceId, date, staffId } = req.body;

        if (!serviceId || !date) {
            const errorId = errorUtil.generateErrorId();

            logger.warn('Missing required parameters', { errorId });

            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: 'serviceId/date',
                        message:
                            'Missing required parameters: serviceId and date',
                    },
                ],
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                400,
                errorId
            );

            return res.status(400).json(errorResponse);
        }

        const result = await AvailableTimeSlot.getAvailableTimeSlots({
            serviceId,
            date,
            staffId,
        });

        return res.status(200).json(result);
    } catch (err) {
        const errorId = errorUtil.generateErrorId();

        logger.error(
            `Internal error during availability check: ${err.message}`,
            {
                errorId,
            }
        );

        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
}

async function checkAvailableStaffForTimeSlot(req, res) {
    try {
        const { serviceId, date } = req.body;

        // Validate required parameters
        if (!serviceId || !date) {
            const errorId = errorUtil.generateErrorId();

            logger.warn('Missing required parameters', { errorId });

            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: 'serviceId/date',
                        message:
                            'Missing required parameters: serviceId, date.',
                    },
                ],
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                400,
                errorId
            );

            return res.status(400).json(errorResponse);
        }

        // Call the service function
        const result = await AvailableTimeSlot.getAvailableTimeSlotsWithStaff(
            serviceId,
            date
        );

        return res.status(200).json(result);
    } catch (err) {
        const errorId = errorUtil.generateErrorId();

        logger.error(
            `Internal error during staff availability check: ${err.message}`,
            { errorId }
        );

        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
}

async function checkAvailableStaff(req, res) {
    try {
        const { serviceId, date } = req.body;

        if (!serviceId || !date) {
            const errorId = errorUtil.generateErrorId();

            logger.warn('Missing required parameters', { errorId });

            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: 'serviceId/date',
                        message:
                            'Missing required parameters: serviceId and date',
                    },
                ],
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                400,
                errorId
            );

            return res.status(400).json(errorResponse);
        }

        const result = await AvailableTimeSlot.getAvailableStaff(
            serviceId,
            date
        );

        return res.status(200).json(result);
    } catch (err) {
        const errorId = errorUtil.generateErrorId();

        logger.error(
            `Internal error during staff availability check: ${err.message}`,
            { errorId }
        );

        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
}

async function checkAvailabilityDateRange(req, res) {
    try {
        const { serviceId, startDate, endDate } = req.body;

        if (!serviceId || !startDate || !endDate) {
            const errorId = errorUtil.generateErrorId();

            logger.warn('Missing required parameters', { errorId });

            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: 'serviceId/startDate/ endDate',
                        message:
                            'Missing required parameters: serviceId and startDate, endDate',
                    },
                ],
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                400,
                errorId
            );

            return res.status(400).json(errorResponse);
        }

        const result = await AvailableTimeSlot.checkAvailabilityDateRange(
            serviceId,
            startDate,
            endDate
        );

        return res.status(200).json(result);
    } catch (err) {
        const errorId = errorUtil.generateErrorId();

        logger.error(
            `Internal error during date availability check: ${err.message}`,
            { errorId }
        );

        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
}
module.exports = {
    createBooking,
    getBookings,
    getBookingById,
    updateBooking,
    updateBookingStatus,
    deleteBooking,
    checkAvailabilityByService,
    rescheduleBooking,
    checkAvailableStaff,
    checkAvailabilityDateRange,
    getBookingByReferenceCode,
    checkAvailableStaffForTimeSlot,
};
