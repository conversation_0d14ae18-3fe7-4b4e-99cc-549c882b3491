/* eslint-disable id-length */
const AdditionalService = require('../model/additionalServiceModel');

const Gallery = require('../model/galleryModel');

const Location = require('../model/locationModel');

const Seo = require('../model/seoModel');

const ServiceAvailability = require('../model/serviceAvailabilityModel');

const Faq = require('../model/faqModel');

const logger = require('../../../common/utils/logger');

/**
 * Helper function to fetch related data
 * @param {Object} service - The service object
 * @param {String} fieldName - The field name to search in
 * @param {Model} model - The model to use for fetching documents
 * @param {String} idFieldName - The ID field name
 * @returns {Promise<Array>} - The fetched related data
 */
const fetchRelatedData = async (service, fieldName, model, idFieldName) => {
    try {
        logger.info(
            `Fetching related data for field: ${fieldName} using model: ${model.modelName}`
        );

        if (service[fieldName].length > 0) {
            logger.info(
                `Found ${service[fieldName].length} items for field: ${fieldName}`
            );
            const relatedItems = await model.find({
                [idFieldName]: { $in: service[fieldName] },
            });

            return relatedItems.map((item) => {
                const result = { id: item[idFieldName] };

                // Include specific fields depending on the model
                if (model === AdditionalService) {
                    result.serviceItem = item.serviceItem;
                    result.price = item.price;
                    result.images = item.images;
                } else if (model === ServiceAvailability) {
                    result.day = item.day;
                    result.timeSlots = item.timeSlots.map((slot) => {
                        const availableSlots =
                            (slot.maxBookings || 1) - (slot.booked || 0);
                        return {
                            from: slot.from,
                            to: slot.to,
                            maxBookings: slot.maxBookings || 1,
                            availableSlots,
                            booked: slot.booked || 0,
                        };
                    });
                } else if (model === Location) {
                    result.address = item.address;
                    result.city = item.city;
                    result.state = item.state;
                    result.country = item.country;
                    result.pinCode = item.pinCode;
                    result.googleMapsPlaceId = item.googleMapsPlaceId;
                    result.latitude = item.latitude;
                    result.longitude = item.longitude;
                } else if (model === Gallery) {
                    result.serviceImages = item.serviceImages;
                    result.serviceVideo = item.serviceVideo;
                    result.videoLink = item.videoLink;
                } else if (model === Seo) {
                    result.metaTitle = item.metaTitle;
                    result.metaKeywords = item.metaKeywords;
                    result.metaDescription = item.metaDescription;
                } else if (model === Faq) {
                    result.question = item.question;
                    result.answer = item.answer;
                }

                return result;
            });
        }

        logger.info(`No data found for field: ${fieldName}`);
        return [];
    } catch (error) {
        logger.error(`Error fetching ${model.modelName}:`, error);
        return [];
    }
};

// Fetch Additional Services
const fetchAdditionalServices = async (service) => {
    try {
        logger.info('Fetching Additional Services...');
        const additionalServices = await fetchRelatedData(
            service,
            'additionalServicesId',
            AdditionalService,
            'additionalServiceId'
        );
        logger.info('Successfully fetched Additional Services.');
        return additionalServices;
    } catch (error) {
        logger.error('Error fetching Additional Services:', error);
        throw error;
    }
};

// Fetch Service Availability
const fetchServiceAvailability = async (service) => {
    try {
        logger.info('Fetching Service Availability...');
        const availability = await fetchRelatedData(
            service,
            'serviceAvailableId',
            ServiceAvailability,
            'serviceAvailableId'
        );
        logger.info('Successfully fetched Service Availability.');
        return availability;
    } catch (error) {
        logger.error('Error fetching Service Availability:', error);
        throw error;
    }
};

// Fetch Location
const fetchLocation = async (service) => {
    try {
        logger.info('Fetching Locations...');
        const locations = await fetchRelatedData(
            service,
            'locationId',
            Location,
            'locationId'
        );
        logger.info('Successfully fetched Locations.');
        return locations;
    } catch (error) {
        logger.error('Error fetching Locations:', error);
        throw error;
    }
};

// Fetch Gallery
const fetchGallery = async (service) => {
    try {
        logger.info('Fetching Gallery...');
        const gallery = await fetchRelatedData(
            service,
            'galleryId',
            Gallery,
            'galleryId'
        );
        logger.info('Successfully fetched Gallery.');
        return gallery;
    } catch (error) {
        logger.error('Error fetching Gallery:', error);
        throw error;
    }
};

const fetchFaq = async (service) => {
    try {
        logger.info('Fetching FAQ...');
        const faq = await fetchRelatedData(service, 'faqId', Faq, 'faqId');
        logger.info('Successfully fetched FAQ.');
        return faq;
    } catch (error) {
        logger.error('Error fetching FAQ:', error);
        throw error;
    }
};
// Fetch SEO
const fetchSeo = async (service) => {
    try {
        logger.info('Fetching SEO data...');
        const seoData = await fetchRelatedData(service, 'seoId', Seo, 'seoId');
        logger.info('Successfully fetched SEO data.');
        return seoData;
    } catch (error) {
        logger.error('Error fetching SEO data:', error);
        throw error;
    }
};

const updateFieldIfChanged = (field, newValue, service, updatedFields) => {
    let hasChanges = false;

    if (
        newValue !== undefined &&
        newValue !== null &&
        newValue !== service[field]
    ) {
        updatedFields[field] = newValue;
        hasChanges = true;
    }

    return hasChanges;
};

const updateRelatedData = async (
    field,
    updateMethod,
    data,
    serviceId,
    updatedFields,
    relatedDataUpdates
) => {
    if (data) {
        const result = await updateMethod(data, serviceId);
        if (result.length > 0) {
            updatedFields[field] = result;
            relatedDataUpdates.push(field);
        }
    }
};

const validateLocations = (locations) => {
    locations.forEach((location) => {
        if (
            !location.address ||
            !location.city ||
            !location.state ||
            !location.country ||
            !location.pinCode ||
            !location.latitude ||
            !location.longitude
        ) {
            throw new Error(
                'Location data is incomplete: Missing required fields.'
            );
        }
    });
};

module.exports = {
    validateLocations,
    updateRelatedData,
    updateFieldIfChanged,
    fetchAdditionalServices,
    fetchServiceAvailability,
    fetchLocation,
    fetchGallery,
    fetchSeo,
    fetchFaq,
};
