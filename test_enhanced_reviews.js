// Simple test script to verify enhanced reviews functionality
const mongoose = require('mongoose');
const ReviewService = require('./src/api/v1/Reviews/service/reviewService');

// Test the enhanced getReviews function
async function testEnhancedReviews() {
    try {
        console.log('Testing enhanced getReviews function...');
        
        // Test basic functionality
        const result = await ReviewService.getReviews({}, 'reviewDate', -1, 1, 5);
        
        console.log('Basic getReviews result:');
        console.log('- Total reviews:', result.total);
        console.log('- Reviews returned:', result.reviews.length);
        console.log('- Pagination info:', {
            page: result.page,
            pages: result.pages,
            hasNext: result.hasNext,
            hasPrev: result.hasPrev
        });
        
        if (result.reviews.length > 0) {
            const firstReview = result.reviews[0];
            console.log('- First review enhanced fields:');
            console.log('  - helpfulVotesCount:', firstReview.helpfulVotesCount);
            console.log('  - notHelpfulVotesCount:', firstReview.notHelpfulVotesCount);
            console.log('  - helpfulnessPercentage:', firstReview.helpfulnessPercentage);
            console.log('  - imageCount:', firstReview.imageCount);
            console.log('  - hasProviderResponse:', firstReview.hasProviderResponse);
            console.log('  - formattedReviewDate:', firstReview.formattedReviewDate);
            console.log('  - daysSinceReview:', firstReview.daysSinceReview);
        }
        
        console.log('\nTesting getReviewsForFrontend function...');
        
        // Test frontend-specific function
        const frontendResult = await ReviewService.getReviewsForFrontend({
            page: 1,
            limit: 5,
            sortBy: 'reviewDate',
            sortOrder: 'desc'
        });
        
        console.log('Frontend getReviews result:');
        console.log('- Total reviews:', frontendResult.pagination.totalItems);
        console.log('- Reviews returned:', frontendResult.reviews.length);
        console.log('- Pagination info:', frontendResult.pagination);
        
        if (frontendResult.reviews.length > 0) {
            const firstReview = frontendResult.reviews[0];
            console.log('- First review frontend fields:');
            console.log('  - helpfulVotesCount:', firstReview.helpfulVotesCount);
            console.log('  - totalVotes:', firstReview.totalVotes);
            console.log('  - helpfulnessPercentage:', firstReview.helpfulnessPercentage);
            console.log('  - hasImages:', firstReview.hasImages);
            console.log('  - hasProviderResponse:', firstReview.hasProviderResponse);
        }
        
        console.log('\n✅ Enhanced reviews functionality test completed successfully!');
        
    } catch (error) {
        console.error('❌ Error testing enhanced reviews:', error.message);
        console.error(error.stack);
    }
}

// Test individual review by ID
async function testEnhancedReviewById() {
    try {
        console.log('\nTesting enhanced getReviewById function...');
        
        // First get a review ID from the list
        const listResult = await ReviewService.getReviews({}, 'reviewDate', -1, 1, 1);
        
        if (listResult.reviews.length === 0) {
            console.log('No reviews found to test getReviewById');
            return;
        }
        
        const reviewId = listResult.reviews[0].reviewId;
        console.log('Testing with reviewId:', reviewId);
        
        const review = await ReviewService.getReviewById(reviewId);
        
        console.log('Enhanced getReviewById result:');
        console.log('- Review ID:', review.reviewId);
        console.log('- Rating:', review.rating);
        console.log('- Enhanced fields:');
        console.log('  - helpfulVotesCount:', review.helpfulVotesCount);
        console.log('  - notHelpfulVotesCount:', review.notHelpfulVotesCount);
        console.log('  - helpfulnessPercentage:', review.helpfulnessPercentage);
        console.log('  - imageCount:', review.imageCount);
        console.log('  - hasProviderResponse:', review.hasProviderResponse);
        console.log('  - formattedReviewDate:', review.formattedReviewDate);
        console.log('  - daysSinceReview:', review.daysSinceReview);
        
        console.log('\n✅ Enhanced getReviewById test completed successfully!');
        
    } catch (error) {
        console.error('❌ Error testing enhanced getReviewById:', error.message);
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    console.log('🚀 Starting enhanced reviews functionality tests...\n');
    
    // Note: You'll need to connect to your MongoDB database first
    // mongoose.connect('your-mongodb-connection-string');
    
    testEnhancedReviews()
        .then(() => testEnhancedReviewById())
        .then(() => {
            console.log('\n🎉 All tests completed!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 Test suite failed:', error);
            process.exit(1);
        });
}

module.exports = {
    testEnhancedReviews,
    testEnhancedReviewById
};
