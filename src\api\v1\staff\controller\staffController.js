const { validationResult } = require('express-validator');

const { InsufficientScopeError } = require('express-oauth2-jwt-bearer');

const staffService = require('../service/staffService');

const logger = require('../../../common/utils/logger');

const errorUtil = require('../../../common/utils/error');

const {
    validateAndParseParams,
    buildQueryFilter,
} = require('../../../common/utils/filter');

const createStaff = async (req, res) => {
    try {
        logger.info('Received request to create staff');

        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const formattedErrors = errors.array().map((err) => ({
                field: err.path,
                message: err.msg,
            }));

            const errorId = errorUtil.generateErrorId(); // Generate errorId
            const errorResponse = errorUtil.createErrorResponse(
                formattedErrors,
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                422,
                errorId
            );
            logger.error(`Validation error for request: ${formattedErrors}`, {
                errorId,
                formattedErrors,
            });
            return res.status(422).json(errorResponse);
        }

        const staffData = req.body;
        logger.info('Creating staff with data');

        const providerId = req.userData.user.userId;

        const staff = await staffService.createStaff(staffData, providerId);
        logger.info(`Staff created successfully: ${staff.id}`);

        return res.status(201).json({
            success: true,
            message: 'Staff created successfully',
            staff,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();

        if (error instanceof InsufficientScopeError) {
            logger.error(`Insufficient scope error: ${error.message}`, {
                errorId,
            });
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'scope', message: error.message }],
                errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
                403,
                errorId
            );
            return res.status(403).json(errorResponse);
        }

        logger.error(`Error creating staff: ${error.message}`, { errorId });
        const errorResponse = error.createErrorResponse(
            [{ field: 'staff', message: error.message }],
            error.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const getStaff = async (req, res) => {
    try {
        const userRole = req.userData.user.role;

        const userProviderId = req.userData.user.userId;

        let ProviderId;

        if (userRole === 'admin') {
            ProviderId = req.query.providerId || null;
        } else if (userRole === 'provider') {
            ProviderId = userProviderId;
        }

        logger.info('Received request to get staff');

        const {
            page = '1',
            limit = '10',
            search,
            sortBy = 'updatedAt',
            sortOrder = 'desc',
        } = req.query;

        logger.info(
            `Fetching staff for role: ${userRole}, ProviderId: ${ProviderId}`
        );

        const { pageNum, limitNum, sortDirection } = validateAndParseParams(
            page == 'undefined' ? '1' : page,
            limit,
            sortOrder
        );
        const query = buildQueryFilter(search, ProviderId);

        const { staffList, total } = await staffService.getStaff(
            query,
            sortBy,
            sortDirection,
            pageNum,
            limitNum
        );

        const totalPages = limitNum > 0 ? Math.ceil(total / limitNum) : 0;

        logger.info(
            `Staff fetched successfully. Total records: ${total}, Total pages: ${totalPages}`
        );

        return res.status(200).json({
            success: true,
            message: 'Staff fetched successfully',
            staff: staffList,
            total,
            page: pageNum,
            pages: totalPages,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(`Error fetching staff: ${error.message}`, { errorId });

        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const getStaffById = async (req, res) => {
    try {
        const { staffId } = req.params;

        const errorId = errorUtil.generateErrorId(); // Generate errorId
        logger.info(`Received request to get staff with ID: ${staffId}`);

        if (!staffId) {
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'staffId', message: 'Staff ID is required' }],
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                400,
                errorId
            );
            return res.status(400).json(errorResponse);
        }

        const staff = await staffService.getStaffById(staffId);

        if (!staff) {
            logger.warn(`Staff with ID ${staffId} not found`, { errorId });
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'staffId', message: 'Staff not found' }],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        logger.info(`Staff with ID ${staffId} found`, { errorId });

        return res.status(200).json({
            success: true,
            message: 'Staff fetched successfully',
            staff,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId(); // Generate errorId
        logger.error(`Error in getStaffById: ${error.message}`, { errorId });
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const updateStaff = async (req, res) => {
    try {
        const { staffId } = req.params;
        const userRole = req.userData.user.role;
        const userProviderId = req.userData.user.userId;

        const errorId = errorUtil.generateErrorId();
        logger.info(`Received request to update staff with ID: ${staffId}`, {
            errorId,
        });

        const existingStaff = await staffService.getStaffById(staffId);

        if (!existingStaff) {
            logger.warn(`Staff with ID ${staffId} not found for update`, {
                errorId,
            });
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'staffId', message: 'Staff not found for update' }],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        if (
            userRole !== 'admin' &&
            existingStaff.providerId !== userProviderId
        ) {
            logger.warn(`Unauthorized access to staff by ${userRole}`, {
                errorId,
            });
            const errorResponse = errorUtil.createErrorResponse(
                ['You are not authorized to update this staff member'],
                errorUtil.ERROR_TYPES.UNAUTHORIZED,
                403,
                errorId
            );
            return res.status(403).json(errorResponse);
        }

        const updatedStaff = await staffService.updateStaff(staffId, req.body);

        logger.info(`Staff with ID ${staffId} updated successfully`, {
            errorId,
        });

        return res.status(200).json({
            success: true,
            message: 'Staff updated successfully',
            staff: updatedStaff,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error updating staff with ID ${req.params.staffId}: ${error.message}`,
            { errorId }
        );
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const deleteStaff = async (req, res) => {
    const { staffId } = req.params;
    const userRole = req.userData.user.role;
    const userProviderId = req.userData.user.userId;

    try {
        const errorId = errorUtil.generateErrorId();
        logger.info(`Received request to delete staff with ID: ${staffId}`, {
            errorId,
        });

        const staff = await staffService.getStaffById(staffId);
        if (!staff) {
            logger.error(`Staff with ID ${staffId} not found`, { errorId });
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'staffId', message: 'Staff not found' }],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        if (userRole !== 'admin' && staff.providerId !== userProviderId) {
            logger.warn(`Unauthorized deletion attempt by ${userRole}`, {
                errorId,
            });
            const errorResponse = errorUtil.createErrorResponse(
                ['You are not authorized to delete this staff member'],
                errorUtil.ERROR_TYPES.UNAUTHORIZED,
                403,
                errorId
            );
            return res.status(403).json(errorResponse);
        }

        await staffService.deleteStaff(staffId);

        logger.info(`Staff with ID ${staffId} deleted successfully`, {
            errorId,
        });

        return res.status(200).json({
            success: true,
            message: 'Staff deleted successfully',
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error deleting staff with ID ${staffId}: ${error.message}`,
            { errorId }
        );
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

module.exports = {
    createStaff,
    getStaff,
    getStaffById,
    updateStaff,
    deleteStaff,
};
